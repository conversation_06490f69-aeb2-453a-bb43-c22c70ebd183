/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
'use strict';var n;function aa(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}}var ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a};
function ca(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");}var p=ca(this);function da(a,b){if(b)a:{var c=p;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}}
da("Symbol",function(a){function b(e){if(this instanceof b)throw new TypeError("Symbol is not a constructor");return new c("jscomp_symbol_"+(e||"")+"_"+d++,e)}function c(e,f){this.g=e;ba(this,"description",{configurable:!0,writable:!0,value:f})}if(a)return a;c.prototype.toString=function(){return this.g};var d=0;return b});
da("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=p[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return fa(aa(this))}})}return a});function fa(a){a={next:a};a[Symbol.iterator]=function(){return this};return a}
function ha(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];return b?b.call(a):{next:aa(a)}}var ia="function"==typeof Object.create?Object.create:function(a){function b(){}b.prototype=a;return new b},ja;if("function"==typeof Object.setPrototypeOf)ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},na={};try{na.__proto__=la;ka=na.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var oa=ja;function q(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(oa)oa(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.L=b.prototype}function pa(){this.l=!1;this.i=null;this.o=void 0;this.g=1;this.m=this.h=0;this.j=null}function qa(a){if(a.l)throw new TypeError("Generator is already running");a.l=!0}pa.prototype.s=function(a){this.o=a};
function ra(a,b){a.j={ca:b,ra:!0};a.g=a.h||a.m}pa.prototype.return=function(a){this.j={return:a};this.g=this.m};function r(a,b,c){a.g=c;return{value:b}}function sa(a,b){a.g=b;a.h=0}function ta(a){a.h=0;var b=a.j.ca;a.j=null;return b}function ua(a){this.g=new pa;this.h=a}function va(a,b){qa(a.g);var c=a.g.i;if(c)return wa(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return xa(a)}
function wa(a,b,c,d){try{var e=b.call(a.g.i,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.g.l=!1,e;var f=e.value}catch(g){return a.g.i=null,ra(a.g,g),xa(a)}a.g.i=null;d.call(a.g,f);return xa(a)}function xa(a){for(;a.g.g;)try{var b=a.h(a.g);if(b)return a.g.l=!1,{value:b.value,done:!1}}catch(c){a.g.o=void 0,ra(a.g,c)}a.g.l=!1;if(a.g.j){b=a.g.j;a.g.j=null;if(b.ra)throw b.ca;return{value:b.return,done:!0}}return{value:void 0,done:!0}}
function ya(a){this.next=function(b){qa(a.g);a.g.i?b=wa(a,a.g.i.next,b,a.g.s):(a.g.s(b),b=xa(a));return b};this.throw=function(b){qa(a.g);a.g.i?b=wa(a,a.g.i["throw"],b,a.g.s):(ra(a.g,b),b=xa(a));return b};this.return=function(b){return va(a,b)};this[Symbol.iterator]=function(){return this}}function za(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new Promise(function(d,e){function f(g){g.done?d(g.value):Promise.resolve(g.value).then(b,c).then(f,e)}f(a.next())})}
function Aa(a){return za(new ya(new ua(a)))}
da("Promise",function(a){function b(g){this.g=0;this.i=void 0;this.h=[];this.s=!1;var h=this.j();try{g(h.resolve,h.reject)}catch(k){h.reject(k)}}function c(){this.g=null}function d(g){return g instanceof b?g:new b(function(h){h(g)})}if(a)return a;c.prototype.h=function(g){if(null==this.g){this.g=[];var h=this;this.i(function(){h.o()})}this.g.push(g)};var e=p.setTimeout;c.prototype.i=function(g){e(g,0)};c.prototype.o=function(){for(;this.g&&this.g.length;){var g=this.g;this.g=[];for(var h=0;h<g.length;++h){var k=
g[h];g[h]=null;try{k()}catch(l){this.j(l)}}}this.g=null};c.prototype.j=function(g){this.i(function(){throw g;})};b.prototype.j=function(){function g(l){return function(m){k||(k=!0,l.call(h,m))}}var h=this,k=!1;return{resolve:g(this.F),reject:g(this.o)}};b.prototype.F=function(g){if(g===this)this.o(new TypeError("A Promise cannot resolve to itself"));else if(g instanceof b)this.I(g);else{a:switch(typeof g){case "object":var h=null!=g;break a;case "function":h=!0;break a;default:h=!1}h?this.D(g):this.l(g)}};
b.prototype.D=function(g){var h=void 0;try{h=g.then}catch(k){this.o(k);return}"function"==typeof h?this.J(h,g):this.l(g)};b.prototype.o=function(g){this.m(2,g)};b.prototype.l=function(g){this.m(1,g)};b.prototype.m=function(g,h){if(0!=this.g)throw Error("Cannot settle("+g+", "+h+"): Promise already settled in state"+this.g);this.g=g;this.i=h;2===this.g&&this.G();this.v()};b.prototype.G=function(){var g=this;e(function(){if(g.C()){var h=p.console;"undefined"!==typeof h&&h.error(g.i)}},1)};b.prototype.C=
function(){if(this.s)return!1;var g=p.CustomEvent,h=p.Event,k=p.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof g?g=new g("unhandledrejection",{cancelable:!0}):"function"===typeof h?g=new h("unhandledrejection",{cancelable:!0}):(g=p.document.createEvent("CustomEvent"),g.initCustomEvent("unhandledrejection",!1,!0,g));g.promise=this;g.reason=this.i;return k(g)};b.prototype.v=function(){if(null!=this.h){for(var g=0;g<this.h.length;++g)f.h(this.h[g]);this.h=null}};var f=new c;b.prototype.I=
function(g){var h=this.j();g.U(h.resolve,h.reject)};b.prototype.J=function(g,h){var k=this.j();try{g.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};b.prototype.then=function(g,h){function k(I,B){return"function"==typeof I?function(G){try{l(I(G))}catch(ea){m(ea)}}:B}var l,m,C=new b(function(I,B){l=I;m=B});this.U(k(g,l),k(h,m));return C};b.prototype.catch=function(g){return this.then(void 0,g)};b.prototype.U=function(g,h){function k(){switch(l.g){case 1:g(l.i);break;case 2:h(l.i);break;default:throw Error("Unexpected state: "+
l.g);}}var l=this;null==this.h?f.h(k):this.h.push(k);this.s=!0};b.resolve=d;b.reject=function(g){return new b(function(h,k){k(g)})};b.race=function(g){return new b(function(h,k){for(var l=ha(g),m=l.next();!m.done;m=l.next())d(m.value).U(h,k)})};b.all=function(g){var h=ha(g),k=h.next();return k.done?d([]):new b(function(l,m){function C(G){return function(ea){I[G]=ea;B--;0==B&&l(I)}}var I=[],B=0;do I.push(void 0),B++,d(k.value).U(C(I.length-1),m),k=h.next();while(!k.done)})};return b});
function t(a,b){return Object.prototype.hasOwnProperty.call(a,b)}
da("WeakMap",function(a){function b(k){this.g=(h+=Math.random()+1).toString();if(k){k=ha(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}}function c(){}function d(k){var l=typeof k;return"object"===l&&null!==k||"function"===l}function e(k){if(!t(k,g)){var l=new c;ba(k,g,{value:l})}}function f(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof c)return m;Object.isExtensible(m)&&e(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),
m=new a([[k,2],[l,3]]);if(2!=m.get(k)||3!=m.get(l))return!1;m.delete(k);m.set(l,4);return!m.has(k)&&4==m.get(l)}catch(C){return!1}}())return a;var g="$jscomp_hidden_"+Math.random();f("freeze");f("preventExtensions");f("seal");var h=0;b.prototype.set=function(k,l){if(!d(k))throw Error("Invalid WeakMap key");e(k);if(!t(k,g))throw Error("WeakMap key fail: "+k);k[g][this.g]=l;return this};b.prototype.get=function(k){return d(k)&&t(k,g)?k[g][this.g]:void 0};b.prototype.has=function(k){return d(k)&&t(k,
g)&&t(k[g],this.g)};b.prototype.delete=function(k){return d(k)&&t(k,g)&&t(k[g],this.g)?delete k[g][this.g]:!1};return b});
da("Map",function(a){function b(){var h={};return h.previous=h.next=h.head=h}function c(h,k){var l=h.g;return fa(function(){if(l){for(;l.head!=h.g;)l=l.previous;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})}function d(h,k){var l=k&&typeof k;"object"==l||"function"==l?f.has(k)?l=f.get(k):(l=""+ ++g,f.set(k,l)):l="p_"+k;var m=h.h[l];if(m&&t(h.h,l))for(h=0;h<m.length;h++){var C=m[h];if(k!==k&&C.key!==C.key||k===C.key)return{id:l,list:m,index:h,entry:C}}return{id:l,
list:m,index:-1,entry:void 0}}function e(h){this.h={};this.g=b();this.size=0;if(h){h=ha(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}}if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(ha([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=h||"s"!=m.value[1])return!1;m=l.next();return m.done||4!=
m.value[0].x||"t"!=m.value[1]||!l.next().done?!1:!0}catch(C){return!1}}())return a;var f=new WeakMap;e.prototype.set=function(h,k){h=0===h?0:h;var l=d(this,h);l.list||(l.list=this.h[l.id]=[]);l.entry?l.entry.value=k:(l.entry={next:this.g,previous:this.g.previous,head:this.g,key:h,value:k},l.list.push(l.entry),this.g.previous.next=l.entry,this.g.previous=l.entry,this.size++);return this};e.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this.h[h.id],
h.entry.previous.next=h.entry.next,h.entry.next.previous=h.entry.previous,h.entry.head=null,this.size--,!0):!1};e.prototype.clear=function(){this.h={};this.g=this.g.previous=b();this.size=0};e.prototype.has=function(h){return!!d(this,h).entry};e.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};e.prototype.entries=function(){return c(this,function(h){return[h.key,h.value]})};e.prototype.keys=function(){return c(this,function(h){return h.key})};e.prototype.values=function(){return c(this,
function(h){return h.value})};e.prototype.forEach=function(h,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=m.value,h.call(k,m[1],m[0],this)};e.prototype[Symbol.iterator]=e.prototype.entries;var g=0;return e});var u=this||self;function Ba(){}function Ca(a){var b=typeof a;b="object"!=b?b:a?Array.isArray(a)?"array":b:"null";return"array"==b||"object"==b&&"number"==typeof a.length}function v(a){var b=typeof a;return"object"==b&&null!=a||"function"==b}
function Da(a,b,c){return a.call.apply(a.bind,arguments)}function Ea(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}}function w(a,b,c){Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?w=Da:w=Ea;return w.apply(null,arguments)}
function Fa(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}}function x(a,b){function c(){}c.prototype=b.prototype;a.L=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.xa=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}}function Ga(a){return a};function Ha(a){if(Error.captureStackTrace)Error.captureStackTrace(this,Ha);else{var b=Error().stack;b&&(this.stack=b)}a&&(this.message=String(a))}x(Ha,Error);Ha.prototype.name="CustomError";var Ia;var Ja=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},Ka=Array.prototype.forEach?function(a,b,c){Array.prototype.forEach.call(a,b,c)}:function(a,b,c){for(var d=a.length,e="string"===typeof a?a.split(""):a,f=0;f<d;f++)f in e&&b.call(c,e[f],f,a)},La=Array.prototype.filter?function(a,b){return Array.prototype.filter.call(a,
b,void 0)}:function(a,b){for(var c=a.length,d=[],e=0,f="string"===typeof a?a.split(""):a,g=0;g<c;g++)if(g in f){var h=f[g];b.call(void 0,h,g,a)&&(d[e++]=h)}return d};function Ma(a){return Array.prototype.concat.apply([],arguments)}function Na(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};function Oa(a,b,c){for(var d in a)b.call(c,a[d],d,a)}var Pa="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" ");function Qa(a,b){for(var c,d,e=1;e<arguments.length;e++){d=arguments[e];for(c in d)a[c]=d[c];for(var f=0;f<Pa.length;f++)c=Pa[f],Object.prototype.hasOwnProperty.call(d,c)&&(a[c]=d[c])}};var Ra;function Sa(a,b){this.g=a===Ta&&b||"";this.h=Ua}Sa.prototype.ga=!0;Sa.prototype.fa=function(){return this.g};var Ua={},Ta={};function Va(a,b){this.g=b===Wa?a:""}Va.prototype.ga=!0;Va.prototype.fa=function(){return this.g.toString()};Va.prototype.toString=function(){return this.g+""};function Xa(a){return a instanceof Va&&a.constructor===Va?a.g:"type_error:TrustedResourceUrl"}var Wa={};
function Ya(a){if(void 0===Ra){var b=null;var c=u.trustedTypes;if(c&&c.createPolicy){try{b=c.createPolicy("goog#html",{createHTML:Ga,createScript:Ga,createScriptURL:Ga})}catch(d){u.console&&u.console.error(d.message)}Ra=b}else Ra=b}a=(b=Ra)?b.createScriptURL(a):a;return new Va(a,Wa)};var Za=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]},$a=/&/g,ab=/</g,bb=/>/g,cb=/"/g,db=/'/g,eb=/\x00/g,fb=/[\x00&<>"']/;function gb(a,b){return a<b?-1:a>b?1:0};var hb;a:{var ib=u.navigator;if(ib){var jb=ib.userAgent;if(jb){hb=jb;break a}}hb=""}function y(a){return-1!=hb.indexOf(a)};function kb(a){fb.test(a)&&(-1!=a.indexOf("&")&&(a=a.replace($a,"&amp;")),-1!=a.indexOf("<")&&(a=a.replace(ab,"&lt;")),-1!=a.indexOf(">")&&(a=a.replace(bb,"&gt;")),-1!=a.indexOf('"')&&(a=a.replace(cb,"&quot;")),-1!=a.indexOf("'")&&(a=a.replace(db,"&#39;")),-1!=a.indexOf("\x00")&&(a=a.replace(eb,"&#0;")));return a};function lb(a){lb[" "](a);return a}lb[" "]=Ba;function mb(a,b,c){return Object.prototype.hasOwnProperty.call(a,b)?a[b]:a[b]=c(b)};var nb=y("Opera"),z=y("Trident")||y("MSIE"),ob=y("Edge"),pb=y("Gecko")&&!(-1!=hb.toLowerCase().indexOf("webkit")&&!y("Edge"))&&!(y("Trident")||y("MSIE"))&&!y("Edge"),qb=-1!=hb.toLowerCase().indexOf("webkit")&&!y("Edge");function rb(){var a=u.document;return a?a.documentMode:void 0}var sb;
a:{var tb="",ub=function(){var a=hb;if(pb)return/rv:([^\);]+)(\)|;)/.exec(a);if(ob)return/Edge\/([\d\.]+)/.exec(a);if(z)return/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(a);if(qb)return/WebKit\/(\S+)/.exec(a);if(nb)return/(?:Version)[ \/]?(\S+)/.exec(a)}();ub&&(tb=ub?ub[1]:"");if(z){var vb=rb();if(null!=vb&&vb>parseFloat(tb)){sb=String(vb);break a}}sb=tb}var wb=sb,xb={};
function A(a){return mb(xb,a,function(){for(var b=0,c=Za(String(wb)).split("."),d=Za(String(a)).split("."),e=Math.max(c.length,d.length),f=0;0==b&&f<e;f++){var g=c[f]||"",h=d[f]||"";do{g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];h=/(\d*)(\D*)(.*)/.exec(h)||["","","",""];if(0==g[0].length&&0==h[0].length)break;b=gb(0==g[1].length?0:parseInt(g[1],10),0==h[1].length?0:parseInt(h[1],10))||gb(0==g[2].length,0==h[2].length)||gb(g[2],h[2]);g=g[3];h=h[3]}while(0==b)}return 0<=b})}var yb;
if(u.document&&z){var zb=rb();yb=zb?zb:parseInt(wb,10)||void 0}else yb=void 0;var Ab=yb;function Bb(a,b,c,d){this.h=a;this.da=b;this.g=c;this.Z=d;this.sa=0};function D(){}var Cb="function"==typeof Uint8Array;function E(a,b,c,d,e){a.g=null;b||(b=[]);a.s=void 0;a.i=-1;a.B=b;a:{var f=a.B.length;b=-1;if(f&&(b=f-1,f=a.B[b],!(null===f||"object"!=typeof f||Array.isArray(f)||Cb&&f instanceof Uint8Array))){a.j=b-a.i;a.h=f;break a}-1<c?(a.j=Math.max(c,b+1-a.i),a.h=null):a.j=Number.MAX_VALUE}a.l={};if(d)for(c=0;c<d.length;c++)b=d[c],b<a.j?(b+=a.i,a.B[b]=a.B[b]||Db):(Eb(a),a.h[b]=a.h[b]||Db);if(e&&e.length)for(c=0;c<e.length;c++)Fb(a,e[c])}var Db=[];
function Eb(a){var b=a.j+a.i;a.B[b]||(a.h=a.B[b]={})}function Gb(a,b,c){for(var d=[],e=0;e<a.length;e++)d[e]=b.call(a[e],c,a[e]);return d}function F(a,b){if(b<a.j){b+=a.i;var c=a.B[b];return c!==Db?c:a.B[b]=[]}if(a.h)return c=a.h[b],c===Db?a.h[b]=[]:c}function Hb(a,b){a=F(a,b);return null==a?a:!!a}function H(a,b,c){b<a.j?a.B[b+a.i]=c:(Eb(a),a.h[b]=c);return a}function Fb(a,b){for(var c,d,e=0;e<b.length;e++){var f=b[e],g=F(a,f);null!=g&&(c=f,d=g,H(a,f,void 0))}return c?(H(a,c,d),c):0}
function J(a,b,c){a.g||(a.g={});if(!a.g[c]){var d=F(a,c);d&&(a.g[c]=new b(d))}return a.g[c]}function Ib(a,b,c){a.g||(a.g={});if(!a.g[c]){for(var d=F(a,c),e=[],f=0;f<d.length;f++)e[f]=new b(d[f]);a.g[c]=e}b=a.g[c];b==Db&&(b=a.g[c]=[]);return b}function Jb(a,b,c){a.g||(a.g={});var d=c?c.B:c;a.g[b]=c;return H(a,b,d)}function Kb(a,b,c){var d=Lb[0];a.g||(a.g={});var e=c?c.B:c;a.g[b]=c;(c=Fb(a,d))&&c!==b&&void 0!==e&&(a.g&&c in a.g&&(a.g[c]=void 0),H(a,c,void 0));return H(a,b,e)}
function Mb(a,b,c){a.g||(a.g={});c=c||[];for(var d=[],e=0;e<c.length;e++)d[e]=c[e].B;a.g[b]=c;return H(a,b,d)}D.prototype.toString=function(){return this.B.toString()};D.prototype.o=function(a){Eb(this);this.g||(this.g={});var b=a.h;return a.g?(!this.g[b]&&this.h[b]&&(this.g[b]=new a.g(this.h[b])),this.g[b]):this.h[b]};function Nb(a){E(this,a,-1,null,null)}x(Nb,D);function Ob(a){E(this,a,-1,null,null)}x(Ob,D);Ob.prototype.setEnabled=function(a){return H(this,2,a)};function Pb(a){E(this,a,-1,null,null)}x(Pb,D);Pb.prototype.setEnabled=function(a){return H(this,2,a)};function Qb(a){E(this,a,-1,Rb,null)}x(Qb,D);var Rb=[3];function Sb(a){E(this,a,-1,Tb,null)}x(Sb,D);var Tb=[3];function Ub(a){E(this,a,-1,Vb,null)}x(Ub,D);var Vb=[2];function Wb(a,b){return Mb(a,2,b)};function Xb(a){E(this,a,-1,null,null)}x(Xb,D);function Yb(a,b){return H(a,2,b)}function Zb(a,b){return Jb(a,3,b)};function $b(a){E(this,a,-1,null,Lb)}x($b,D);var Lb=[[4,5,6,7,8]];function ac(a){var b=new $b;return H(b,3,a)}function bc(a,b){return Kb(a,5,b)};function K(){}K.prototype.toString=function(){var a=L(cc(M(this.constructor)))+"@";var b=((this.j||(Object.defineProperties(this,{j:{value:++dc,enumerable:!1}}),this.j))>>>0).toString(16);return a+L(b)};K.prototype.u=["java.lang.Object",0];function N(){}q(N,K);N.prototype.h=function(a){this.g=a;ec(this,a)};function fc(a){gc(a.g)&&(Error.captureStackTrace?Error.captureStackTrace(hc(a.g,gc,ic)):hc(a.g,gc,ic).stack=Error().stack)}N.prototype.toString=function(){var a=cc(M(this.constructor)),b=this.i;return null==b?a:L(a)+": "+L(b)};function jc(a){return a instanceof N}N.prototype.u=["java.lang.Throwable",0];
function ec(a,b){if(b instanceof Object)try{b.wa=a,Object.defineProperties(b,{cause:{get:function(){return a.o&&a.o.g}}})}catch(c){}};function kc(){}q(kc,N);kc.prototype.u=["java.lang.Exception",0];function lc(){}q(lc,kc);lc.prototype.u=["java.lang.RuntimeException",0];function mc(){}q(mc,lc);mc.prototype.u=["java.lang.IndexOutOfBoundsException",0];var dc=0;function nc(){}q(nc,N);nc.prototype.u=["java.lang.Error",0];function hc(a,b,c){if(null!=a&&!b(a))throw a=L(cc(oc(a)))+" cannot be cast to "+L(cc(M(c))),b=new pc,b.i=a,fc(b),b.h(Error(b)),b.g;return a};function qc(){}q(qc,nc);qc.prototype.u=["java.lang.AssertionError",0];function pc(){}q(pc,lc);pc.prototype.u=["java.lang.ClassCastException",0];function rc(){}q(rc,mc);rc.prototype.u=["java.lang.StringIndexOutOfBoundsException",0];function sc(){}q(sc,lc);sc.prototype.u=["java.util.NoSuchElementException",0];function tc(){}q(tc,K);tc.prototype.u=["java.lang.Boolean",0];function uc(){}q(uc,K);uc.prototype.u=["java.lang.Number",0];function vc(){}q(vc,uc);vc.prototype.u=["java.lang.Double",0];function wc(){}wc.prototype.u=["<native function>",1];function xc(){}q(xc,K);xc.prototype.u=["<native object>",0];function oc(a){var b=typeof a;if("number"==b)return M(vc);if("boolean"==b)return M(tc);if("string"==b)return M(yc);if(Array.isArray(a))return(a=a.va)?M(a.Ga,a.Aa):M(K,1);if(a instanceof K)return M(a.constructor);if(a)return M("function"==b?wc:xc);throw new TypeError("null.getClass");};function yc(){}q(yc,K);function L(a){return null==a?"null":a.toString()}yc.prototype.u=["java.lang.String",0];function zc(a,b){this.g=a;this.h=b}q(zc,K);function cc(a){var b=a.g.prototype.u[0];0!=a.h&&(3==a.g.prototype.u[1]?b=a.g.prototype.u[2]:b="L"+L(b)+";");return L(Ac("[",a.h))+L(b)}function Bc(a,b){b=a.lastIndexOf(b)+1|0;var c=a.length+1|0;if(0>b||b>=c)throw a=new rc,a.i="Index: "+b+", Size: "+c,fc(a),a.h(Error(a)),a.g;return a.substr(b)}zc.prototype.toString=function(){return String(0==this.h&&1==this.g.prototype.u[1]?"interface ":0==this.h&&3==this.g.prototype.u[1]?"":"class ")+L(cc(this))};
function Ac(a,b){for(var c="",d=0;d<b;d=d+1|0)c=L(c)+L(a);return c}zc.prototype.u=["java.lang.Class",0];function M(a,b){var c=b||0;return mb(a.prototype,"$$class/"+c,function(){return new zc(a,c)})};function ic(){}function gc(a){return a instanceof Error}ic.prototype.u=["Error",0];function Cc(a){a&&"function"==typeof a.N&&a.N()};function Dc(a){for(var b=0,c=arguments.length;b<c;++b){var d=arguments[b];Ca(d)?Dc.apply(null,d):Cc(d)}};function O(){this.o=this.o;this.j=this.j}O.prototype.o=!1;O.prototype.ha=function(){return this.o};O.prototype.N=function(){this.o||(this.o=!0,this.A())};function P(a,b){Ec(a,Fa(Cc,b))}function Ec(a,b,c){a.o?void 0!==c?b.call(c):b():(a.j||(a.j=[]),a.j.push(void 0!==c?w(b,c):b))}O.prototype.A=function(){if(this.j)for(;this.j.length;)this.j.shift()()};function Fc(){O.call(this);this.g=null}q(Fc,O);function Gc(a,b,c){a.ha()||(Hc(a),a.g=new Image,a.g.onload=function(){c&&c(!0);Hc(a)},a.g.onerror=function(){c&&c(!1);Hc(a)},a.g.src=b)}function Hc(a){if(a.g)try{a.g.onload=null,a.g.onerror=null,a.g=null}catch(b){}}Fc.prototype.A=function(){Hc(this)};function Q(a,b){this.type=a;this.g=this.target=b;this.defaultPrevented=!1}Q.prototype.i=function(){this.defaultPrevented=!0};function Ic(a,b){a instanceof Error||(a=Error(a),Error.captureStackTrace&&Error.captureStackTrace(a,Ic));a.stack||(a.stack=Jc(Ic));if(b){for(var c=0;a["message"+c];)++c;a["message"+c]=String(b)}return a}function Kc(a,b){a=Ic(a);if(b)for(var c in b){var d=a,e=c,f=b[c];d.__closure__error__context__984382||(d.__closure__error__context__984382={});d.__closure__error__context__984382[e]=f}return a}
function Jc(a){var b=Error();if(Error.captureStackTrace)Error.captureStackTrace(b,a||Jc),b=String(b.stack);else{try{throw b;}catch(c){b=c}b=(b=b.stack)?String(b):null}b||(b=Lc(a||arguments.callee.caller,[]));return b}
function Lc(a,b){var c=[];if(0<=Ja(b,a))c.push("[...circular reference...]");else if(a&&50>b.length){c.push(Mc(a)+"(");for(var d=a.arguments,e=0;d&&e<d.length;e++){0<e&&c.push(", ");var f=d[e];switch(typeof f){case "object":f=f?"object":"null";break;case "string":break;case "number":f=String(f);break;case "boolean":f=f?"true":"false";break;case "function":f=(f=Mc(f))?f:"[fn]";break;default:f=typeof f}40<f.length&&(f=f.substr(0,40)+"...");c.push(f)}b.push(a);c.push(")\n");try{c.push(Lc(a.caller,b))}catch(g){c.push("[exception trying to get caller]\n")}}else a?
c.push("[...long stack...]"):c.push("[end]");return c.join("")}function Mc(a){if(Nc[a])return Nc[a];a=String(a);if(!Nc[a]){var b=/function\s+([^\(]+)/m.exec(a);Nc[a]=b?b[1]:"[Anonymous]"}return Nc[a]}var Nc={};var Oc=!z||9<=Number(Ab),Pc=z&&!A("9"),Qc=!qb||A("528"),Rc=pb&&A("1.9b")||z&&A("8")||nb&&A("9.5")||qb&&A("528"),Sc=pb&&!A("8")||z&&!A("9"),Tc=function(){if(!u.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{u.addEventListener("test",Ba,b),u.removeEventListener("test",Ba,b)}catch(c){}return a}();function Uc(a,b){Q.call(this,a?a.type:"");this.relatedTarget=this.g=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=0;this.key="";this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.h=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.g=b;if(b=a.relatedTarget){if(pb){a:{try{lb(b.nodeName);var e=!0;break a}catch(f){}e=
!1}e||(b=null)}}else"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.key=a.key||"";this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=
a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:Vc[a.pointerType]||"";this.state=a.state;this.h=a;a.defaultPrevented&&this.i()}}x(Uc,Q);var Vc={2:"touch",3:"pen",4:"mouse"};Uc.prototype.i=function(){Uc.L.i.call(this);var a=this.h;if(a.preventDefault)a.preventDefault();else if(a.returnValue=!1,Pc)try{if(a.ctrlKey||112<=a.keyCode&&123>=a.keyCode)a.keyCode=-1}catch(b){}};var Wc="closure_listenable_"+(1E6*Math.random()|0);function Xc(a){return!(!a||!a[Wc])};var Yc=0;function Zc(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.P=e;this.key=++Yc;this.removed=this.T=!1}function $c(a){a.removed=!0;a.listener=null;a.proxy=null;a.src=null;a.P=null};function ad(a){this.src=a;this.g={};this.h=0}ad.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.g[f];a||(a=this.g[f]=[],this.h++);var g=bd(a,b,d,e);-1<g?(b=a[g],c||(b.T=!1)):(b=new Zc(b,this.src,f,!!d,e),b.T=c,a.push(b));return b};ad.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.g))return!1;var e=this.g[a];b=bd(e,b,c,d);return-1<b?($c(e[b]),Array.prototype.splice.call(e,b,1),0==e.length&&(delete this.g[a],this.h--),!0):!1};
function cd(a,b){var c=b.type;if(c in a.g){var d=a.g[c],e=Ja(d,b),f;(f=0<=e)&&Array.prototype.splice.call(d,e,1);f&&($c(b),0==a.g[c].length&&(delete a.g[c],a.h--))}}ad.prototype.removeAll=function(a){a=a&&a.toString();var b=0,c;for(c in this.g)if(!a||c==a){for(var d=this.g[c],e=0;e<d.length;e++)++b,$c(d[e]);delete this.g[c];this.h--}return b};function dd(a,b,c,d,e){a=a.g[b.toString()];b=-1;a&&(b=bd(a,c,d,e));return-1<b?a[b]:null}
function bd(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.removed&&f.listener==b&&f.capture==!!c&&f.P==d)return e}return-1};var ed="closure_lm_"+(1E6*Math.random()|0),fd={},gd=0;function hd(a,b,c,d,e){if(d&&d.once)return id(a,b,c,d,e);if(Array.isArray(b)){for(var f=0;f<b.length;f++)hd(a,b[f],c,d,e);return null}c=jd(c);return Xc(a)?a.listen(b,c,v(d)?!!d.capture:!!d,e):kd(a,b,c,!1,d,e)}
function kd(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=v(e)?!!e.capture:!!e,h=ld(a);h||(a[ed]=h=new ad(a));c=h.add(b,c,d,g,f);if(c.proxy)return c;d=md();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)Tc||(e=g),void 0===e&&(e=!1),a.addEventListener(b.toString(),d,e);else if(a.attachEvent)a.attachEvent(nd(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");gd++;return c}
function md(){var a=od,b=Oc?function(c){return a.call(b.src,b.listener,c)}:function(c){c=a.call(b.src,b.listener,c);if(!c)return c};return b}function id(a,b,c,d,e){if(Array.isArray(b)){for(var f=0;f<b.length;f++)id(a,b[f],c,d,e);return null}c=jd(c);return Xc(a)?a.h.add(String(b),c,!0,v(d)?!!d.capture:!!d,e):kd(a,b,c,!0,d,e)}
function pd(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)pd(a,b[f],c,d,e);else d=v(d)?!!d.capture:!!d,c=jd(c),Xc(a)?a.h.remove(String(b),c,d,e):a&&(a=ld(a))&&(b=dd(a,b,c,d,e))&&qd(b)}
function qd(a){if("number"!==typeof a&&a&&!a.removed){var b=a.src;if(Xc(b))cd(b.h,a);else{var c=a.type,d=a.proxy;b.removeEventListener?b.removeEventListener(c,d,a.capture):b.detachEvent?b.detachEvent(nd(c),d):b.addListener&&b.removeListener&&b.removeListener(d);gd--;(c=ld(b))?(cd(c,a),0==c.h&&(c.src=null,b[ed]=null)):$c(a)}}}function nd(a){return a in fd?fd[a]:fd[a]="on"+a}
function rd(a,b,c,d){var e=!0;if(a=ld(a))if(b=a.g[b.toString()])for(b=b.concat(),a=0;a<b.length;a++){var f=b[a];f&&f.capture==c&&!f.removed&&(f=sd(f,d),e=e&&!1!==f)}return e}function sd(a,b){var c=a.listener,d=a.P||a.src;a.T&&qd(a);return c.call(d,b)}
function od(a,b){if(a.removed)return!0;if(!Oc){if(!b)a:{b=["window","event"];for(var c=u,d=0;d<b.length;d++)if(c=c[b[d]],null==c){b=null;break a}b=c}d=b;b=new Uc(d,this);c=!0;if(!(0>d.keyCode||void 0!=d.returnValue)){a:{var e=!1;if(0==d.keyCode)try{d.keyCode=-1;break a}catch(g){e=!0}if(e||void 0==d.returnValue)d.returnValue=!0}d=[];for(e=b.g;e;e=e.parentNode)d.push(e);a=a.type;for(e=d.length-1;0<=e;e--){b.g=d[e];var f=rd(d[e],a,!0,b);c=c&&f}for(e=0;e<d.length;e++)b.g=d[e],f=rd(d[e],a,!1,b),c=c&&f}return c}return sd(a,
new Uc(b,this))}function ld(a){a=a[ed];return a instanceof ad?a:null}var td="__closure_events_fn_"+(1E9*Math.random()>>>0);function jd(a){if("function"===typeof a)return a;a[td]||(a[td]=function(b){return a.handleEvent(b)});return a[td]};function R(){O.call(this);this.h=new ad(this);this.I=this;this.s=null}x(R,O);R.prototype[Wc]=!0;R.prototype.removeEventListener=function(a,b,c,d){pd(this,a,b,c,d)};
function S(a,b){var c,d=a.s;if(d)for(c=[];d;d=d.s)c.push(d);a=a.I;d=b.type||b;if("string"===typeof b)b=new Q(b,a);else if(b instanceof Q)b.target=b.target||a;else{var e=b;b=new Q(d,a);Qa(b,e)}e=!0;if(c)for(var f=c.length-1;0<=f;f--){var g=b.g=c[f];e=ud(g,d,!0,b)&&e}g=b.g=a;e=ud(g,d,!0,b)&&e;e=ud(g,d,!1,b)&&e;if(c)for(f=0;f<c.length;f++)g=b.g=c[f],e=ud(g,d,!1,b)&&e}R.prototype.A=function(){R.L.A.call(this);this.h&&this.h.removeAll(void 0);this.s=null};
R.prototype.listen=function(a,b,c,d){return this.h.add(String(a),b,!1,c,d)};function ud(a,b,c,d){b=a.h.g[String(b)];if(!b)return!0;b=b.concat();for(var e=!0,f=0;f<b.length;++f){var g=b[f];if(g&&!g.removed&&g.capture==c){var h=g.listener,k=g.P||g.src;g.T&&cd(a.h,g);e=!1!==h.call(k,d)&&e}}return e&&!d.defaultPrevented};function vd(a,b){this.i=a;this.j=b;this.h=0;this.g=null}vd.prototype.get=function(){if(0<this.h){this.h--;var a=this.g;this.g=a.next;a.next=null}else a=this.i();return a};function wd(a,b){a.j(b);100>a.h&&(a.h++,b.next=a.g,a.g=b)};var xd=!z||9<=Number(Ab),yd=!pb&&!z||z&&9<=Number(Ab)||pb&&A("1.9.1");function zd(a,b){Oa(b,function(c,d){c&&"object"==typeof c&&c.ga&&(c=c.fa());"style"==d?a.style.cssText=c:"class"==d?a.className=c:"for"==d?a.htmlFor=c:Ad.hasOwnProperty(d)?a.setAttribute(Ad[d],c):0==d.lastIndexOf("aria-",0)||0==d.lastIndexOf("data-",0)?a.setAttribute(d,c):a[d]=c})}
var Ad={cellpadding:"cellPadding",cellspacing:"cellSpacing",colspan:"colSpan",frameborder:"frameBorder",height:"height",maxlength:"maxLength",nonce:"nonce",role:"role",rowspan:"rowSpan",type:"type",usemap:"useMap",valign:"vAlign",width:"width"};function Bd(a){return a.parentWindow||a.defaultView}
function Cd(a,b,c){function d(h){h&&b.appendChild("string"===typeof h?a.createTextNode(h):h)}for(var e=2;e<c.length;e++){var f=c[e];if(!Ca(f)||v(f)&&0<f.nodeType)d(f);else{a:{if(f&&"number"==typeof f.length){if(v(f)){var g="function"==typeof f.item||"string"==typeof f.item;break a}if("function"===typeof f){g="function"==typeof f.item;break a}}g=!1}Ka(g?Na(f):f,d)}}}function Dd(a,b){b=String(b);"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)}
function Ed(){this.g=u.document||document}Ed.prototype.setProperties=zd;Ed.prototype.h=function(a,b,c){var d=this.g,e=arguments,f=String(e[0]),g=e[1];if(!xd&&g&&(g.name||g.type)){f=["<",f];g.name&&f.push(' name="',kb(g.name),'"');if(g.type){f.push(' type="',kb(g.type),'"');var h={};Qa(h,g);delete h.type;g=h}f.push(">");f=f.join("")}f=Dd(d,f);g&&("string"===typeof g?f.className=g:Array.isArray(g)?f.className=g.join(" "):zd(f,g));2<e.length&&Cd(d,f,e);return f};
Ed.prototype.getChildren=function(a){return yd&&void 0!=a.children?a.children:La(a.childNodes,function(b){return 1==b.nodeType})};Ed.prototype.contains=function(a,b){if(!a||!b)return!1;if(a.contains&&1==b.nodeType)return a==b||a.contains(b);if("undefined"!=typeof a.compareDocumentPosition)return a==b||!!(a.compareDocumentPosition(b)&16);for(;b&&a!=b;)b=b.parentNode;return b==a};var Fd;
function Gd(){var a=u.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!y("Presto")&&(a=function(){var e=Dd(document,"IFRAME");e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var g="callImmediate"+Math.random(),h="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=w(function(k){if(("*"==h||k.origin==h)&&k.data==g)this.port1.onmessage()},this);
f.addEventListener("message",e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(g,h)}}});if("undefined"!==typeof a&&!y("Trident")&&!y("MSIE")){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.ba;c.ba=null;e()}};return function(e){d.next={ba:e};d=d.next;b.port2.postMessage(0)}}return function(e){u.setTimeout(e,0)}};function Hd(a){u.setTimeout(function(){throw a;},0)};function Id(){this.h=this.g=null}Id.prototype.add=function(a,b){var c=Jd.get();c.set(a,b);this.h?this.h.next=c:this.g=c;this.h=c};Id.prototype.remove=function(){var a=null;this.g&&(a=this.g,this.g=this.g.next,this.g||(this.h=null),a.next=null);return a};var Jd=new vd(function(){return new Kd},function(a){return a.reset()});function Kd(){this.next=this.h=this.g=null}Kd.prototype.set=function(a,b){this.g=a;this.h=b;this.next=null};Kd.prototype.reset=function(){this.next=this.h=this.g=null};function Ld(a,b){Md||Nd();Od||(Md(),Od=!0);Pd.add(a,b)}var Md;function Nd(){if(u.Promise&&u.Promise.resolve){var a=u.Promise.resolve(void 0);Md=function(){a.then(Qd)}}else Md=function(){var b=Qd;"function"!==typeof u.setImmediate||u.Window&&u.Window.prototype&&!y("Edge")&&u.Window.prototype.setImmediate==u.setImmediate?(Fd||(Fd=Gd()),Fd(b)):u.setImmediate(b)}}var Od=!1,Pd=new Id;function Qd(){for(var a;a=Pd.remove();){try{a.g.call(a.h)}catch(b){Hd(b)}wd(Jd,a)}Od=!1};function T(a){this.g=0;this.s=void 0;this.j=this.h=this.i=null;this.o=this.l=!1;if(a!=Ba)try{var b=this;a.call(void 0,function(c){Rd(b,2,c)},function(c){Rd(b,3,c)})}catch(c){Rd(this,3,c)}}function Sd(){this.next=this.j=this.h=this.o=this.g=null;this.i=!1}Sd.prototype.reset=function(){this.j=this.h=this.o=this.g=null;this.i=!1};var Td=new vd(function(){return new Sd},function(a){a.reset()});function Ud(a,b,c){var d=Td.get();d.o=a;d.h=b;d.j=c;return d}
function Vd(a){if(a instanceof T)return a;var b=new T(Ba);Rd(b,2,a);return b}function U(){var a,b,c=new T(function(d,e){a=d;b=e});return new Wd(c,a,b)}T.prototype.then=function(a,b,c){return Xd(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};T.prototype.$goog_Thenable=!0;function Yd(a,b){b=Ud(b,b,void 0);b.i=!0;Zd(a,b)}function V(a,b){return Xd(a,null,b,void 0)}T.prototype.cancel=function(a){if(0==this.g){var b=new $d(a);Ld(function(){ae(this,b)},this)}};
function ae(a,b){if(0==a.g)if(a.i){var c=a.i;if(c.h){for(var d=0,e=null,f=null,g=c.h;g&&(g.i||(d++,g.g==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.g&&1==d?ae(c,b):(f?(d=f,d.next==c.j&&(c.j=d),d.next=d.next.next):be(c),ce(c,e,3,b)))}a.i=null}else Rd(a,3,b)}function Zd(a,b){a.h||2!=a.g&&3!=a.g||de(a);a.j?a.j.next=b:a.h=b;a.j=b}
function Xd(a,b,c,d){var e=Ud(null,null,null);e.g=new T(function(f,g){e.o=b?function(h){try{var k=b.call(d,h);f(k)}catch(l){g(l)}}:f;e.h=c?function(h){try{var k=c.call(d,h);void 0===k&&h instanceof $d?g(h):f(k)}catch(l){g(l)}}:g});e.g.i=a;Zd(a,e);return e.g}T.prototype.v=function(a){this.g=0;Rd(this,2,a)};T.prototype.C=function(a){this.g=0;Rd(this,3,a)};
function Rd(a,b,c){if(0==a.g){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.g=1;a:{var d=c,e=a.v,f=a.C;if(d instanceof T){Zd(d,Ud(e||Ba,f||null,a));var g=!0}else{if(d)try{var h=!!d.$goog_Thenable}catch(l){h=!1}else h=!1;if(h)d.then(e,f,a),g=!0;else{if(v(d))try{var k=d.then;if("function"===typeof k){ee(d,k,e,f,a);g=!0;break a}}catch(l){f.call(a,l);g=!0;break a}g=!1}}}g||(a.s=c,a.g=b,a.i=null,de(a),3!=b||c instanceof $d||fe(a,c))}}
function ee(a,b,c,d,e){function f(k){h||(h=!0,d.call(e,k))}function g(k){h||(h=!0,c.call(e,k))}var h=!1;try{b.call(a,g,f)}catch(k){f(k)}}function de(a){a.l||(a.l=!0,Ld(a.m,a))}function be(a){var b=null;a.h&&(b=a.h,a.h=b.next,b.next=null);a.h||(a.j=null);return b}T.prototype.m=function(){for(var a;a=be(this);)ce(this,a,this.g,this.s);this.l=!1};
function ce(a,b,c,d){if(3==c&&b.h&&!b.i)for(;a&&a.o;a=a.i)a.o=!1;if(b.g)b.g.i=null,ge(b,c,d);else try{b.i?b.o.call(b.j):ge(b,c,d)}catch(e){he.call(null,e)}wd(Td,b)}function ge(a,b,c){2==b?a.o.call(a.j,c):a.h&&a.h.call(a.j,c)}function fe(a,b){a.o=!0;Ld(function(){a.o&&he.call(null,b)})}var he=Hd;function $d(a){Ha.call(this,a)}x($d,Ha);$d.prototype.name="cancel";function Wd(a,b,c){this.promise=a;this.resolve=b;this.reject=c};function ie(a,b){R.call(this);this.i=a||1;this.g=b||u;this.l=w(this.ua,this);this.m=Date.now()}x(ie,R);n=ie.prototype;n.V=!1;n.H=null;n.ua=function(){if(this.V){var a=Date.now()-this.m;0<a&&a<.8*this.i?this.H=this.g.setTimeout(this.l,this.i-a):(this.H&&(this.g.clearTimeout(this.H),this.H=null),S(this,"tick"),this.V&&(this.stop(),this.start()))}};n.start=function(){this.V=!0;this.H||(this.H=this.g.setTimeout(this.l,this.i),this.m=Date.now())};
n.stop=function(){this.V=!1;this.H&&(this.g.clearTimeout(this.H),this.H=null)};n.A=function(){ie.L.A.call(this);this.stop();delete this.g};function je(a,b){if("function"!==typeof a)if(a&&"function"==typeof a.handleEvent)a=w(a.handleEvent,a);else throw Error("Invalid listener argument");return 2147483647<Number(b)?-1:u.setTimeout(a,b||0)}
function ke(){var a=null;return V(new T(function(b,c){a=je(function(){b(void 0)},3E4);-1==a&&c(Error("Failed to schedule timer."))}),function(b){u.clearTimeout(a);throw b;})};function le(a,b,c){O.call(this);this.g=a;this.l=b||0;this.h=c;this.i=w(this.la,this)}x(le,O);n=le.prototype;n.O=0;n.A=function(){le.L.A.call(this);this.stop();delete this.g;delete this.h};n.start=function(a){this.stop();this.O=je(this.i,void 0!==a?a:this.l)};n.stop=function(){0!=this.O&&u.clearTimeout(this.O);this.O=0};n.la=function(){this.O=0;this.g&&this.g.call(this.h)};function me(){O.call(this);this.g=null}q(me,O);me.prototype.A=function(){Cc(this.g)};function W(a){O.call(this);this.h=a;this.g={}}x(W,O);var ne=[];W.prototype.listen=function(a,b,c,d){Array.isArray(b)||(b&&(ne[0]=b.toString()),b=ne);for(var e=0;e<b.length;e++){var f=hd(a,b[e],c||this.handleEvent,d||!1,this.h||this);if(!f)break;this.g[f.key]=f}return this};
function oe(a,b,c,d,e,f){if(Array.isArray(c))for(var g=0;g<c.length;g++)oe(a,b,c[g],d,e,f);else d=d||a.handleEvent,e=v(e)?!!e.capture:!!e,f=f||a.h||a,d=jd(d),e=!!e,c=Xc(b)?dd(b.h,String(c),d,e,f):b?(b=ld(b))?dd(b,c,d,e,f):null:null,c&&(qd(c),delete a.g[c.key])}W.prototype.removeAll=function(){Oa(this.g,function(a,b){this.g.hasOwnProperty(b)&&qd(a)},this);this.g={}};W.prototype.A=function(){W.L.A.call(this);this.removeAll()};
W.prototype.handleEvent=function(){throw Error("EventHandler.handleEvent not implemented");};function pe(){R.call(this);this.i=new W(this);Qc&&(Rc?this.i.listen(Sc?document.body:window,["online","offline"],this.l):(this.m=Qc?navigator.onLine:!0,this.g=new ie(250),this.i.listen(this.g,"tick",this.v),this.g.start()))}x(pe,R);pe.prototype.v=function(){var a=Qc?navigator.onLine:!0;a!=this.m&&(this.m=a,this.l())};pe.prototype.l=function(){S(this,(Qc?navigator.onLine:1)?"online":"offline")};pe.prototype.A=function(){pe.L.A.call(this);this.i.N();this.i=null;this.g&&(this.g.N(),this.g=null)};var qe=/^(?:([^:/?#.]+):)?(?:\/\/(?:([^\\/?#]*)@)?([^\\/?#]*?)(?::([0-9]+))?(?=[\\/?#]|$))?([^?#]+)?(?:\?([^#]*))?(?:#([\s\S]*))?$/;function re(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}}
function se(a,b,c){c=null!=c?"="+encodeURIComponent(String(c)):"";if(b+=c){c=a.indexOf("#");0>c&&(c=a.length);var d=a.indexOf("?");if(0>d||d>c){d=c;var e=""}else e=a.substring(d+1,c);a=[a.substr(0,d),e,a.substr(c)];c=a[1];a[1]=b?c?c+"&"+b:b:c;a=a[0]+(a[1]?"?"+a[1]:"")+a[2]}return a}function te(a,b,c){for(;0<=(b=a.indexOf("zx",b))&&b<c;){var d=a.charCodeAt(b-1);if(38==d||63==d)if(d=a.charCodeAt(b+2),!d||61==d||38==d||35==d)return b;b+=3}return-1}var ue=/#|$/,ve=/[?&]($|#)/;
function we(a){for(var b=Math.floor(2147483648*Math.random()).toString(36)+Math.abs(Math.floor(2147483648*Math.random())^Date.now()).toString(36),c=a.search(ue),d=0,e,f=[];0<=(e=te(a,d,c));)f.push(a.substring(d,e)),d=Math.min(a.indexOf("&",e)+1||c,c);f.push(a.substr(d));a=f.join("").replace(ve,"$1");return se(a,"zx",b)};function xe(a,b,c,d,e){R.call(this);var f=this;this.v=new W(this);this.W=a;this.i=new le(function(){f.i.stop();ye(f,ze(f,!1))},1E4);this.G=c||12E4;this.l=new pe;this.g=b;this.F=Date.now();this.m=d||new me;this.D=e||new Fc;this.v.listen(this.l,"online",this.C).listen(this.l,"offline",this.C);this.X=U();ye(this,!0,!0)}q(xe,R);
function ye(a,b,c){var d=Date.now(),e=a.g?25E3:1E4;if(b||!a.g)b=c?0:e,a.F=d-b;else{b=e;c=a.F+e;for(var f=1;c<d&&b<a.G;)b=Math.min(a.G,e*Math.pow(1.2,f++)),c+=b}d=a.m;a=w(a.J,a);e=b;Cc(d.g);d.g=new le(a,e);d.g.start()}xe.prototype.J=function(){ye(this);Ae(this)};function ze(a,b){a.X.resolve();return b!=a.g?(a.g=b,S(a,b?"a":"b"),!0):!1}xe.prototype.C=function(){ye(this,!0,!0)};function Ae(a){a.i.stop();a.i.start();Gc(a.D,we(a.W),function(b){a.i.stop();ye(a,ze(a,b))})}
xe.prototype.A=function(){this.i.stop();Dc(this.m,this.i,this.l,this.v,this.D);R.prototype.A.call(this)};function Be(){this.g=!1}q(Be,K);n=Be.prototype;n.N=function(){if(!this.g){this.g=!0;this.Y();var a=M(this.constructor);Bc(Bc(L(a.g.prototype.u[0])+L(Ac("[]",a.h)),"."),"$")}};n.ha=function(){return this.g};n.Y=function(){if(this.i){var a=new Ce;a.g=this.i;var b=new De;a=a.g;b.g=-1;for(b.h=a;(b.g+1|0)<b.h.length;){a=b;if(!((a.g+1|0)<a.h.length))throw b=new sc,fc(b),b.h(Error(b)),b.g;var c=a.g=a.g+1|0;a.h[c].N()}this.i.length=0}};
n.toString=function(){var a=new qc,b=jc("Attempt to call debug code in non-debug mode.")?hc("Attempt to call debug code in non-debug mode.",jc,N):null;a.o=b;a.i="Attempt to call debug code in non-debug mode.";fc(a);a.h(Error(a));throw a.g;};n.u=["com.google.apps.docs.xplat.disposable.Disposable",0];function Ee(){this.g=0}q(Ee,K);Ee.prototype.u=["com.google.gwt.corp.collections.AbstractJsArray$Iter",0];function De(){this.g=0}q(De,Ee);De.prototype.u=["com.google.gwt.corp.collections.JsArray$Iter",0];function Ce(){}q(Ce,K);Ce.prototype.u=["com.google.gwt.corp.collections.JsArray$1",0];function Fe(){}function Ge(a){return a instanceof Array}Fe.prototype.u=["Array",0];function He(){}q(He,K);function Ie(a){return a instanceof He}He.prototype.u=["com.google.apps.docsshared.xplat.observable.EventObserverTracker$ObservableObserverPair",0];function Je(){Be.call(this);this.h=hc([],Ge,Fe)}q(Je,Be);function Ke(a,b,c){a=a.h;c=b.h(c);var d=new He;d.g=b;d.h=c;a.push(d)}Je.prototype.Y=function(){this.removeAll();Be.prototype.Y.call(this)};Je.prototype.removeAll=function(){for(var a=hc(this.h.pop(),Ie,He);a;)a.g.g(a.h)&&a.g.i(a.h),a=hc(this.h.pop(),Ie,He)};Je.prototype.u=["com.google.apps.docsshared.xplat.observable.EventObserverTracker",0];function Le(a,b,c,d){c=void 0===c?[]:c;d=void 0===d?[]:d;O.call(this);var e=this;this.i=b||this;this.l=a;this.g=new Je;P(this,this.g);this.h=new W(this);P(this,this.h);this.s=c;this.m=d;a instanceof xe?(this.h.listen(a,"a",function(){return Me(e)}),this.h.listen(a,"b",function(){return Ne(e)})):(Ke(this.g,this.l.aa(),function(){return Me(e)}),Ke(this.g,this.l.$(),function(){return Ne(e)}))}q(Le,O);function Oe(a,b){a.s.push(b)}function Me(a){a.s.forEach(function(b){try{b.call(a.i)}catch(c){}})}
function Ne(a){a.m.forEach(function(b){try{b.call(a.i)}catch(c){}})};function Pe(a){E(this,a,-1,null,null)}x(Pe,D);function Qe(){R.call(this);this.g=3;this.i=null;this.l=!1;this.m=U()}q(Qe,R);function Re(a,b,c){if((1==b||2==b)&&!c)throw Error("Eligibility must be provided when transitioning to ENABLED or DISABLED. New state: "+b);c&&(a.i=c);a.g=b;a.m.resolve(null);S(a,"c")}function Se(a){if(!a.i)return!1;a=F(a.i,1);return 1==a||6==a};function Te(a){E(this,a,-1,null,null)}x(Te,D);Te.prototype.getTitle=function(){return F(this,2)};Te.prototype.setTitle=function(a){return H(this,2,a)};function Ue(a){E(this,a,-1,null,null)}x(Ue,D);function Ve(a){var b=new Ue;return H(b,1,a)};function We(a,b){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack);this.ta=b}q(We,Error);function Xe(a,b,c,d){Q.call(this,"broadcast-message",a);this.h=b;this.j=c;this.data=d}q(Xe,Q);function Ye(a){E(this,a,1,null,null)}x(Ye,D);var Ze={};function $e(a){E(this,a,-1,null,null)}x($e,D);Ze[100922934]=new Bb(100922934,{Ca:0},$e,function(a,b){var c,d={Ea:null==(c=F(b,1))?void 0:c};a&&(d.M=b);return d});function af(a){E(this,a,-1,null,null)}x(af,D);function bf(a){var b=new af;return H(b,1,a)}af.prototype.getMessage=function(){return J(this,Ye,2)};function cf(a,b){R.call(this);this.i=!1;this.g=null;this.m=new W(this);P(this,this.m);this.v=a;this.l=b}q(cf,R);cf.prototype.connect=function(){this.i||(this.i=!0,this.g=this.v.g,this.m.listen(this.g,"message",this.C.bind(this)),this.g.start())};cf.prototype.publish=function(a,b){if(!this.i)throw Error("Trying to publish without connecting first.");var c=bf(1);a=Jb(c,2,a);H(a,3,b||this.l);this.g.postMessage(a.B)};
cf.prototype.C=function(a){var b=a.h;null!=b.data[1]?(b=new af(b.data),a=F(b,3),b=b.getMessage()):(a=this.l,b=new Ye(b.data));a:{var c=b;for(e in Ze){var d=Ze[Number(e)];if(c.o(d)){var e=d;break a}}e=null}e&&(b=b.o(e),S(this,new Xe(this,a,e,b)))};cf.prototype.A=function(){if(this.g){var a=bf(0);this.g.postMessage(a.B);this.g.close()}R.prototype.A.call(this)};function df(a,b,c){O.call(this);this.l=a;this.h=b;this.m=c||null;this.g=[];this.i=new W(this);P(this,this.i);this.i.listen(this.l,"broadcast-message",this.s)}q(df,O);df.prototype.listen=function(a,b){this.g.push(new ef(a,b));return this};df.prototype.s=function(a){for(var b=0;b<this.g.length;b++){var c=this.g[b];this.h&&this.h!=a.h||c.g!=a.j||c.P.call(this.m,a.data,a.h)}};function ef(a,b){this.g=a;this.P=b};function ff(a){E(this,a,-1,null,null)}x(ff,D);var gf=new Bb(102041228,{Ia:0},ff,function(a,b){var c={};a&&(c.M=b);return c});Ze[102041228]=gf;function hf(a){E(this,a,-1,null,null)}x(hf,D);var jf=new Bb(108529910,{offlineEnabled:0},hf,function(a,b){var c={};a&&(c.M=b);return c});Ze[108529910]=jf;function kf(a){E(this,a,-1,null,null)}x(kf,D);var lf=new Bb(122453513,{Ja:0},kf,function(a,b){var c,d={Ka:null==(c=F(b,1))?void 0:c,Fa:null==(c=F(b,2))?void 0:c};a&&(d.M=b);return d});Ze[122453513]=lf;function mf(a){E(this,a,-1,null,null)}x(mf,D);function nf(a){E(this,a,-1,null,null)}x(nf,D);function of(a,b){O.call(this);this.l=a;this.v=this.l.g;this.J=b;this.F=new Le(this.l,this,[this.G],[this.D]);P(this,this.F);this.C=this.i=0;this.h=this.s=2E4;this.g=new le(this.I,0,this);P(this,this.g);this.m=!1}q(of,O);function pf(a){if(15<=a.i)return a.g.stop(),!1;if(!a.v&&!a.l.g&&3<=a.C)return!0;qf(a);return!0}function qf(a){15<=a.i||a.m||0!=a.g.O||a.g.start(a.h)}
of.prototype.I=function(){this.J();this.v||this.l.g||this.C++;this.i++;144E5!=this.h&&(this.h=Math.min(this.h+(50+50*Math.random())/100*this.s,144E5),this.s*=2)};of.prototype.G=function(){15<=this.i||(this.h=2E4*Math.random(),this.s=2E4,qf(this))};of.prototype.D=function(){this.g.stop()};function rf(a,b,c,d,e,f,g,h,k,l){k=void 0===k?!1:k;l=void 0===l?!1:l;O.call(this);var m=this;this.g=a;this.D=U();sf(this);this.s=this.v=null;this.ka=g;this.J=new Le(g,this);P(this,this.J);this.h=b;this.F=h||null;this.G=!1;this.i=null;k&&(this.i=new of(this.ka,function(){return tf(m)}),P(this,this.i));this.m=U();this.C=U();this.I=new W(this);P(this,this.I);this.ia="user_"+c;this.X=d;this.aa=e;this.$=f;this.ja=!1;this.l=l?new ie(25E3):null;l&&this.I.listen(this.l,"tick",function(){return uf(m)})}
q(rf,O);n=rf.prototype;n.start=function(){var a=this;this.G||(this.G=!0,vf(this).then(function(){a.m.resolve();if(a.i){var b=a.i;b.g.stop();b.m=!0}},function(b){b=Ic(b);var c=new We(b.message,a.i?a.C.promise:void 0);Kc(c,{originalStackTrace:b.stack});Kc(c,{offlineManagerInitializationFailure:!0,isRetry:!1});a.m.reject(c);a.i?pf(a.i):a.h.m.resolve(c)}));return this.m.promise};
function vf(a){return wf(a.g).then(function(){return xf(a.g)}).then(function(b){b.connect();a.D.resolve(b);return yf(a)}).then(function(){Oe(a.J,function(){zf(a)});a.ja=!0})}function tf(a){a.g.reset();V(vf(a).then(function(){var b=a.i;b.g.stop();b.m=!0;a.C.resolve(a.i.i)}),function(){if(!pf(a.i)){var b=Kc("OfflineService start retries exhausted",{offlineManagerInitializationFailure:!0,isRetry:!0});a.C.reject(b)}})}
function Af(a){if(2!=a.h.g)throw Error("Cannot enable offline when it is not disabled. Current state:"+a.h.g);Re(a.h,4);return Bf(a.g,12).then(function(b){var c=F(b,1);return Vd().then(function(){return 1==c||2==c?X(a,1):X(a,2)}).then(function(){return b})})}
function yf(a){return Cf(a.g,6E4).then(function(b){switch(F(b,1)){case 1:return V(Df(a.g),function(c){a.F&&a.F.g(Ic(c))}).then(function(){return X(a,1)}).then(function(){zf(a)});case 2:return X(a,2);case 4:return Ef(a,F(b,2));case 3:return b=F(b,3),null!=b?Ff(a,b).then(function(c){return c?X(a,1):X(a,2)}):X(a,2);case 5:return Gf(a).then(function(c){return c?X(a,1):X(a,2)})}})}
n.qa=function(a){var b=this;1==this.h.g&&!this.h.l&&0==F(a,2)&&V(Cf(this.g).then(function(c){var d=b.h;c=Hb(c,5);d.l=c;S(d,"c")}),function(){})};function Ef(a){return X(a,2)}function Ff(a,b){return a.X?Bf(a.g,b).then(function(c){switch(F(c,1)){case 1:return!0;case 2:return!0;default:return!1}}):Vd(!1)}function Gf(a){return a.aa?Bf(a.g,2).then(function(b){switch(F(b,1)){case 1:case 2:return!0;default:return!1}}):Vd(!1)}
function sf(a){var b;a.D.promise.then(function(c){b=c;return a.ea()}).then(function(c){a.v=new df(b,c,a);P(a,a.v);a.s=new df(b,null,a);P(a,a.s);a.v.listen(jf,a.pa).listen(gf,a.oa).listen(lf,a.qa);a.s.listen(jf,a.na).listen(gf,a.ma)})}n.pa=function(){V(X(this,1),function(){})};n.oa=function(){V(X(this,2),function(){})};n.na=function(){Hf(this)};n.ma=function(){Hf(this)};
function X(a,b){return If(a.g).then(function(c){Re(a.h,b,c)}).then(function(){return Cf(a.g).then(function(c){var d=a.h;c=!!Hb(c,5);d.l=c;S(d,"c")})}).then(function(){if(a.l)switch(b){case 1:a.l.start();uf(a);break;case 2:a.l.stop()}})}function Jf(a,b,c){return Kf(a.g,b,c).then(function(){})}function Hf(a){If(a.g).then(function(b){var c=a.h;c.i=b;S(c,"c")})}function zf(a){a.$&&1==a.h.g&&V(Lf(a.g),function(){})}n.ea=function(){return Vd(this.ia)};
function uf(a){V(Mf(a.g),function(b){Ic(b);a.l&&a.l.stop()})};function Nf(a){E(this,a,1,null,null)}x(Nf,D);var Of={};function Pf(a,b){var c={},d=Nf.prototype.o,e;for(e in Of){var f=Of[e],g=d.call(b,f);if(null!=g){for(var h in f.da)if(f.da.hasOwnProperty(h))break;c[h]=f.Z?f.sa?Gb(g,f.Z,a):f.Z(a,g):g}}a&&(c.M=b);return c};function Qf(a){E(this,a,-1,Rf,null)}x(Qf,D);var Rf=[1];Ze[113007630]=new Bb(113007630,{za:0},Qf,function(a,b){var c={ya:Gb(Ib(b,Nf,1),Pf,a)};a&&(c.M=b);return c});function Sf(a){E(this,a,-1,Tf,null)}x(Sf,D);var Tf=[3];Of[112987886]=new Bb(112987886,{Da:0},Sf,function(a,b){var c,d={Ba:null==(c=F(b,1))?void 0:c,changeType:null==(c=F(b,2))?void 0:c,Ha:null==(c=F(b,3))?void 0:c};a&&(d.M=b);return d});function Uf(a,b,c){O.call(this);this.m=null!=c?a.bind(c):a;this.s=b;this.l=null;this.h=!1;this.i=0;this.g=null}q(Uf,O);Uf.prototype.stop=function(){this.g&&(u.clearTimeout(this.g),this.g=null,this.h=!1,this.l=null)};Uf.prototype.pause=function(){this.i++};Uf.prototype.resume=function(){this.i--;this.i||!this.h||this.g||(this.h=!1,Vf(this))};Uf.prototype.A=function(){O.prototype.A.call(this);this.stop()};
function Vf(a){a.g=je(function(){a.g=null;a.h&&!a.i&&(a.h=!1,Vf(a))},a.s);var b=a.l;a.l=null;a.m.apply(null,b)};var Wf="StopIteration"in u?u.StopIteration:{message:"StopIteration",stack:""};function Xf(){}Xf.prototype.next=function(){throw Wf;};Xf.prototype.S=function(){return this};function Yf(a){if(a instanceof Xf)return a;if("function"==typeof a.S)return a.S(!1);if(Ca(a)){var b=0,c=new Xf;c.next=function(){for(;;){if(b>=a.length)throw Wf;if(b in a)return a[b++];b++}};return c}throw Error("Not implemented");};function Zf(a){this.g={};if(a)for(var b=0;b<a.length;b++)this.g[$f(a[b])]=null}var ag={};function $f(a){return a in ag||32==String(a).charCodeAt(0)?" "+a:a}function bg(a){return 32==a.charCodeAt(0)?a.substr(1):a}n=Zf.prototype;n.add=function(a){this.g[$f(a)]=null};n.clear=function(){this.g={}};n.contains=function(a){return $f(a)in this.g};n.forEach=function(a,b){for(var c in this.g)a.call(b,bg(c),void 0,this)};
n.K=Object.keys?function(){return Object.keys(this.g).map(bg,this)}:function(){var a=[],b;for(b in this.g)a.push(bg(b));return a};n.remove=function(a){a=$f(a);return a in this.g?(delete this.g[a],!0):!1};n.S=function(){return Yf(this.K())};function cg(a,b,c){R.call(this);this.l=b;this.g=new Zf;this.i=new Zf;this.v=new Uf(this.m,void 0!=c?c:3E3,this);P(this,this.v)}q(cg,R);function dg(a,b){0!=b.length&&V(eg(a.l,b).then(function(c){for(var d=[],e=new Zf(b),f=0;f<c.length;f++){var g=c[f],h=F(g,1);Hb(g,8)&&(d.push(h),e.remove(h))}c=a.g;for(f=0;f<d.length;f++)c.g[$f(d[f])]=null;d=a.g;for(var k in e.g)delete d.g[k];S(a,"d")}),function(c){Ic(c)})}cg.prototype.m=function(){var a=this.i.K();this.i.clear();dg(this,a)};function fg(a){this.g=a};function gg(a){E(this,a,-1,hg,null)}x(gg,D);var hg=[1];function ig(a,b){return H(a,1,b||[])};function jg(a){E(this,a,-1,null,null)}x(jg,D);function kg(a,b){return H(a,1,b)};function lg(a){E(this,a,-1,mg,null)}x(lg,D);var mg=[1];function ng(a,b){return Mb(a,1,b)}function og(a,b){return H(a,2,b)}function pg(a,b){return H(a,3,b)};function qg(a){E(this,a,-1,null,null)}x(qg,D);function Y(a){var b=new qg;return H(b,1,a)};function rg(a){E(this,a,-1,sg,null)}x(rg,D);var sg=[1];function tg(a){E(this,a,-1,null,null)}x(tg,D);function ug(a){E(this,a,-1,null,null)}x(ug,D);function vg(a){E(this,a,-1,null,null)}x(vg,D);function wg(a){E(this,a,-1,null,null)}x(wg,D);function xg(a){E(this,a,-1,null,null)}x(xg,D);function yg(a){E(this,a,-1,null,null)}x(yg,D);function zg(a){E(this,a,-1,null,null)}x(zg,D);function Ag(a,b){this.h={};this.g=[];this.j=this.i=0;var c=arguments.length;if(1<c){if(c%2)throw Error("Uneven number of arguments");for(var d=0;d<c;d+=2)this.set(arguments[d],arguments[d+1])}else if(a)if(a instanceof Ag)for(c=a.R(),d=0;d<c.length;d++)this.set(c[d],a.get(c[d]));else for(d in a)this.set(d,a[d])}n=Ag.prototype;n.K=function(){Bg(this);for(var a=[],b=0;b<this.g.length;b++)a.push(this.h[this.g[b]]);return a};n.R=function(){Bg(this);return this.g.concat()};
n.clear=function(){this.h={};this.j=this.i=this.g.length=0};n.remove=function(a){return Cg(this.h,a)?(delete this.h[a],this.i--,this.j++,this.g.length>2*this.i&&Bg(this),!0):!1};function Bg(a){if(a.i!=a.g.length){for(var b=0,c=0;b<a.g.length;){var d=a.g[b];Cg(a.h,d)&&(a.g[c++]=d);b++}a.g.length=c}if(a.i!=a.g.length){var e={};for(c=b=0;b<a.g.length;)d=a.g[b],Cg(e,d)||(a.g[c++]=d,e[d]=1),b++;a.g.length=c}}n.get=function(a,b){return Cg(this.h,a)?this.h[a]:b};
n.set=function(a,b){Cg(this.h,a)||(this.i++,this.g.push(a),this.j++);this.h[a]=b};n.forEach=function(a,b){for(var c=this.R(),d=0;d<c.length;d++){var e=c[d],f=this.get(e);a.call(b,f,e,this)}};n.S=function(a){Bg(this);var b=0,c=this.j,d=this,e=new Xf;e.next=function(){if(c!=d.j)throw Error("The map has changed since the iterator was created");if(b>=d.g.length)throw Wf;var f=d.g[b++];return a?f:d.h[f]};return e};function Cg(a,b){return Object.prototype.hasOwnProperty.call(a,b)};function Dg(a){this.i=this.m=this.j="";this.s=null;this.o=this.h="";this.l=!1;var b;a instanceof Dg?(this.l=a.l,Eg(this,a.j),this.m=a.m,Fg(this,a.i),Gg(this,a.s),this.h=a.h,Hg(this,Ig(a.g)),this.o=a.o):a&&(b=String(a).match(qe))?(this.l=!1,Eg(this,b[1]||"",!0),this.m=Jg(b[2]||""),Fg(this,b[3]||"",!0),Gg(this,b[4]),this.h=Jg(b[5]||"",!0),Hg(this,b[6]||"",!0),this.o=Jg(b[7]||"")):(this.l=!1,this.g=new Kg(null,this.l))}
Dg.prototype.toString=function(){var a=[],b=this.j;b&&a.push(Lg(b,Mg,!0),":");var c=this.i;if(c||"file"==b)a.push("//"),(b=this.m)&&a.push(Lg(b,Mg,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.s,null!=c&&a.push(":",String(c));if(c=this.h)this.i&&"/"!=c.charAt(0)&&a.push("/"),a.push(Lg(c,"/"==c.charAt(0)?Ng:Og,!0));(c=this.g.toString())&&a.push("?",c);(c=this.o)&&a.push("#",Lg(c,Pg));return a.join("")};
Dg.prototype.resolve=function(a){var b=new Dg(this),c=!!a.j;c?Eg(b,a.j):c=!!a.m;c?b.m=a.m:c=!!a.i;c?Fg(b,a.i):c=null!=a.s;var d=a.h;if(c)Gg(b,a.s);else if(c=!!a.h){if("/"!=d.charAt(0))if(this.i&&!this.h)d="/"+d;else{var e=b.h.lastIndexOf("/");-1!=e&&(d=b.h.substr(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==h?((1<f.length||1==f.length&&
""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?b.h=d:c=""!==a.g.toString();c?Hg(b,Ig(a.g)):c=!!a.o;c&&(b.o=a.o);return b};function Eg(a,b,c){a.j=c?Jg(b,!0):b;a.j&&(a.j=a.j.replace(/:$/,""));return a}function Fg(a,b,c){a.i=c?Jg(b,!0):b;return a}function Gg(a,b){if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.s=b}else a.s=null;return a}function Hg(a,b,c){b instanceof Kg?(a.g=b,Qg(a.g,a.l)):(c||(b=Lg(b,Rg)),a.g=new Kg(b,a.l))}
function Jg(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""}function Lg(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,Sg),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null}function Sg(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)}var Mg=/[#\/\?@]/g,Og=/[#\?:]/g,Ng=/[#\?]/g,Rg=/[#\?@]/g,Pg=/#/g;function Kg(a,b){this.h=this.g=null;this.i=a||null;this.j=!!b}
function Tg(a){a.g||(a.g=new Ag,a.h=0,a.i&&re(a.i,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g," ")),c)}))}n=Kg.prototype;n.add=function(a,b){Tg(this);this.i=null;a=Ug(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.h+=1;return this};n.remove=function(a){Tg(this);a=Ug(this,a);return Cg(this.g.h,a)?(this.i=null,this.h-=this.g.get(a).length,this.g.remove(a)):!1};n.clear=function(){this.g=this.i=null;this.h=0};function Vg(a,b){Tg(a);b=Ug(a,b);return Cg(a.g.h,b)}
n.forEach=function(a,b){Tg(this);this.g.forEach(function(c,d){Ka(c,function(e){a.call(b,e,d,this)},this)},this)};n.R=function(){Tg(this);for(var a=this.g.K(),b=this.g.R(),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};n.K=function(a){Tg(this);var b=[];if("string"===typeof a)Vg(this,a)&&(b=Ma(b,this.g.get(Ug(this,a))));else{a=this.g.K();for(var c=0;c<a.length;c++)b=Ma(b,a[c])}return b};
n.set=function(a,b){Tg(this);this.i=null;a=Ug(this,a);Vg(this,a)&&(this.h-=this.g.get(a).length);this.g.set(a,[b]);this.h+=1;return this};n.get=function(a,b){if(!a)return b;a=this.K(a);return 0<a.length?String(a[0]):b};n.toString=function(){if(this.i)return this.i;if(!this.g)return"";for(var a=[],b=this.g.R(),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.K(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.i=a.join("&")};
function Ig(a){var b=new Kg;b.i=a.i;a.g&&(b.g=new Ag(a.g),b.h=a.h);return b}function Ug(a,b){b=String(b);a.j&&(b=b.toLowerCase());return b}function Qg(a,b){b&&!a.j&&(Tg(a),a.i=null,a.g.forEach(function(c,d){var e=d.toLowerCase();d!=e&&(this.remove(d),this.remove(e),0<c.length&&(this.i=null,this.g.set(Ug(this,e),Na(c)),this.h+=c.length))},a));a.j=b};function Wg(a,b,c,d,e){O.call(this);this.h=U();e||(e=Ia||(Ia=new Ed));this.i=e;this.s=new W(this);P(this,this.s);this.v=a;this.F=d;a=new Dg(b);a.g.set("ouid",this.v);e=new Dg(u.location.href);"true"!=e.g.get("Debug")&&"true"!=e.g.get("debug")&&"pretty"!=e.g.get("debug")&&"DU"!=e.g.get("jsmode")||a.g.set("Debug","true");a.o="cd="+c;if(/^[\s\xa0]*$/.test(a.i))throw Error("Url contains invalid domain: "+b);this.C=Gg(Fg(Eg(new Dg,a.j),a.i),a.s).toString();b=se(a.toString(),"sa",""+d);this.D=Ya(b);this.g=
null;this.l=!1}q(Wg,O);function wf(a){a.s.listen(Bd(a.i.g),"message",a.m);Xg(a);ke().then(function(){a.l||(Yg(a),Zg(a),a.h.reject(Error("Iframe initialization timed out")))});return a.h.promise.then()}Wg.prototype.reset=function(){Zg(this);this.h=U()};function Mf(a){var b=Y(12);return Z(a,b).then(function(){})}function xf(a){var b=Y(1);return Z(a,b).then(function(c){c=new cf(new fg(c.port),"user_"+a.v);P(a,c);return c})}
function eg(a,b){var c=Y(2);b=ig(new gg,b);Jb(c,2,b);return Z(a,c).then(function(d){return Ib(J(d.g,rg,3),Te,1)})}function $g(a){var b=Y(3);return Z(a,b).then(function(c){return Ib(J(c.g,rg,3),Te,1)})}function Cf(a,b){var c=Y(4);return Z(a,c,b).then(function(d){return J(J(d.g,xg,4),nf,1)})}function If(a){var b=Y(10);return Z(a,b).then(function(c){return J(J(c.g,wg,7),Pe,1)})}
function Kf(a,b,c){var d=void 0===d?!1:d;var e=Y(5);b=pg(og(ng(new lg,b),c),d);Jb(e,3,b);return Z(a,e).then(function(f){return(f=J(f.g,yg,8))&&J(f,mf,1)||null})}function Bf(a,b){var c=Y(6);b=kg(new jg,b);Jb(c,4,b);return Z(a,c).then(function(d){return J(J(d.g,tg,5),Pe,1)})}function Df(a){var b=Y(9);return Z(a,b).then(function(){})}function Lf(a){var b=Y(11);return Z(a,b).then(function(){})}function ah(a){var b=Y(13);return Z(a,b).then(function(c){return F(J(c.g,vg,9),1)})}
function Z(a,b,c){c=void 0===c?3E4:c;return V(a.h.promise.then(function(d){var e=Date.now();F(b,1);var f=new MessageChannel;d.postMessage(b.B,[f.port2]);var g=U();f.port1.onmessage=function(k){k=new bh(k);k.g&&J(k.g,ug,2)?(k=J(k.g,ug,2),g.reject(Error("Iframe api request of type "+F(b,1)+" failed (error type "+F(k,1)+"): "+F(k,2)))):g.resolve(k)};var h=je(function(){g.reject(Error("Iframe api request of type "+F(b,1)+" timed out after "+(Date.now()-e)+"ms."))},c);Yd(g.promise,function(){f.port1.close();
u.clearTimeout(h)});return g.promise}),function(d){throw Kc(d,{iframeRequest_sourceApplication:a.F.toString(),iframeRequest_requestType:F(b,1).toString()});})}Wg.prototype.m=function(a){(a=a.h)&&a.origin==this.C&&a.source==this.g.contentWindow&&!this.l&&(a.ports&&a.ports.length&&a.ports[0]&&(a=a.ports[0],Ec(this,a.close,a),this.h.resolve(a),this.l=!0),Yg(this))};function Yg(a){oe(a.s,Bd(a.i.g),"message",a.m)}
function Xg(a){a.g=a.i.h("IFRAME",{style:"display:none"});a.g.src=Xa(a.D).toString();a.i.g.body.appendChild(a.g)}function Zg(a){if(a.g){var b=a.g;b&&b.parentNode&&b.parentNode.removeChild(b);a.g=null}}Wg.prototype.A=function(){Zg(this);O.prototype.A.call(this)};function bh(a){this.g=a.data?new zg(a.data):null;this.port=a.ports&&a.ports.length&&a.ports[0]?a.ports[0]:null};function ch(a,b,c,d,e){b=new Wg(a,Xa(b).toString(),c,d);c=new Qe;rf.call(this,b,c,a,!1,!1,!0,e,void 0,!0);P(this,b);P(this,c);this.W=new cg(c,b);P(this,this.W)}q(ch,rf);ch.prototype.ea=function(){return ah(this.g)};var dh=new Map([["application/vnd.google-apps.document","kix"],["application/vnd.google-apps.drawing","drawing"],["application/vnd.google-apps.presentation","punch"],["application/vnd.google-apps.spreadsheet","ritz"]]);
function eh(a){if(a.sender&&"com.google.drive.nativeproxy"===a.sender.nativeApplication){var b=new fh;a.onDisconnect.addListener(function(){console.log("Native message port disconnected: "+chrome.runtime.lastError.message)});a.onMessage.addListener(function(c){b.onMessage(c,a)})}else console.log("Bad native name passed to native proxy extension."),a.disconnect()}function fh(){this.h=this.g=null}
function gh(a,b){var c,d,e;return Aa(function(f){switch(f.g){case 1:if(a.g)return f.return();a.h=new xe("https://ssl.gstatic.com/docs/common/netcheck.gif",!0);var g=new Sa(Ta,"https://docs.google.com/offline/iframeapi");g=Ya(g instanceof Sa&&g.constructor===Sa&&g.h===Ua?g.g:"type_error:Const");a.g=new ch(b,g,4,11,a.h);f.h=2;return r(f,a.g.start(),4);case 4:sa(f,0);break;case 2:c=ta(f);d=c.ta;if(!d)throw c;f.h=5;return r(f,d(),7);case 7:sa(f,0);break;case 5:throw e=ta(f),e;}})}
fh.prototype.onMessage=function(a,b){var c=this,d,e,f,g,h;return Aa(function(k){switch(k.g){case 1:d=new $b(a);e=F(d,2);if(!e)return b.disconnect(),k.return();k.h=2;return r(k,gh(c,e),4);case 4:sa(k,3);break;case 2:return f=ta(k),console.log("Failed to connect to offline service: "+f),b.disconnect(),k.return();case 3:return k.h=5,r(k,hh(c,d),7);case 7:g=k.o;H(g,2,e);if(null!=F(d,1)){var l=F(d,1);H(g,1,l)}b.postMessage(g.B);sa(k,0);break;case 5:h=ta(k),console.log("Failed to handle message: "+h),b.disconnect(),
k.g=0}})};function hh(a,b){var c;return Aa(function(d){if(7===Fb(b,Lb[0]))return d.return(ih(a,b));c=a.g.h;if(1!=c.g)return Se(c)?d.return(Promise.resolve(ac(23))):d.return(Promise.resolve(ac(10)));switch(Fb(b,Lb[0])){case 4:return d.return(jh(a,b));case 6:return d.return(kh(a,b));default:return d.return(Promise.reject(Error("Unhandled message case: "+Fb(b,Lb[0]))))}})}
function lh(a,b){var c,d;return Aa(function(e){switch(e.g){case 1:return e.h=2,0<b.length?r(e,eg(a.g.g,b),7):r(e,$g(a.g.g),6);case 6:c=e.o;e.g=5;break;case 7:c=e.o;case 5:sa(e,3);break;case 2:return ta(e),e.return(Promise.resolve(bc(new $b,Yb(new Xb,!1))));case 3:return d=[],c.forEach(function(f){var g=Hb(f,7)?2:3;if(0<b.length||2===g){var h=d,k=h.push,l=new Nb;g=H(l,2,g);f=F(f,1);f=H(g,1,f);k.call(h,f)}}),e.return(Promise.resolve(bc(new $b,Yb(Zb(new Xb,Wb(new Ub,d)),!0))))}})}
function ih(a,b){var c,d,e,f;return Aa(function(g){switch(g.g){case 1:c=new Pb;d=a.g.h;if(!Hb(J(b,Ob,7),2)){c.setEnabled(1==d.g);H(c,3,1);g.g=2;break}if(1==d.g){c.setEnabled(!0);H(c,3,3);H(c,4,2);g.g=2;break}if(!Se(d)){c.setEnabled(!1);if(2==d.g){H(c,3,2);var h=F(d.i,1);H(c,4,h)}else H(c,3,0);g.g=2;break}g.h=5;return r(g,Af(a.g),7);case 7:e=g.o;f=F(e,1);1===f?c.setEnabled(!0):(2===f?(c.setEnabled(!0),H(c,3,3)):(c.setEnabled(!1),H(c,3,2)),H(c,4,f));sa(g,2);break;case 5:ta(g),c.setEnabled(1==d.g),H(c,
3,0);case 2:h=g.return;var k=Promise,l=k.resolve;var m=new $b;m=Kb(m,8,c);return h.call(g,l.call(k,m))}})}function jh(a,b){var c;return Aa(function(d){c=J(b,Sb,4);return d.return(lh(a,F(c,3)))})}
function kh(a,b){var c,d,e,f,g,h,k,l,m,C,I;return Aa(function(B){switch(B.g){case 1:c=[];d=[];e=[];f=ha(Ib(J(b,Qb,6),Nb,3));for(g=f.next();!g.done;g=f.next()){h=g.value;k=F(h,1);l=F(h,2);var G=F(h,4);m=G?dh.has(G)?dh.get(G):"unknown":"unknown";if(2===l){G=d;var ea=G.push;var ma=Ve(k);ma=H(ma,2,m);ea.call(G,ma)}else 3===l&&(G=e,ea=G.push,ma=Ve(k),ma=H(ma,2,m),ea.call(G,ma));c.push(k)}C=[];0<d.length&&C.push(Jf(a.g,d,!0));0<e.length&&C.push(Jf(a.g,e,!1));B.h=2;return r(B,Promise.all(C),4);case 4:sa(B,
3);break;case 2:return I=ta(B),console.log("Failed to change pin state. "+I),B.return(Promise.resolve(bc(new $b,Yb(new Xb,!1))));case 3:return B.return(lh(a,c))}})};function mh(a){if(a.name&&"com.google.drive.nativeproxy"===a.name){console.log("Establishing proxy connection to native host: "+a.name);var b=chrome.runtime.connectNative(a.name);b.onDisconnect.addListener(function(){console.log("Native message port disconnected: "+chrome.runtime.lastError.message);a.disconnect()});b.onMessage.addListener(function(c){a.postMessage(c)});a.onDisconnect.addListener(function(){b.disconnect()});a.onMessage.addListener(function(c){b.postMessage(c)})}else console.log("Bad name passed to native proxy extension."),
a.disconnect()};chrome.runtime.onConnectNative.addListener(function(a){eh(a)});chrome.runtime.onConnectExternal.addListener(function(a){mh(a)});
