{"account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "announcement_notification_service_first_run_time": "*****************", "autofill": {"orphan_rows_removed": true}, "bookmark_bar": {"show_on_all_tabs": false}, "bookmarks": {"editing_enabled": false}, "browser": {"has_seen_welcome_page": false}, "countryid_at_install": 19777, "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "last_chrome_version": "107.0.5304.88"}, "gaia_cookie": {"changed_time": **********.805269, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_data": "[\"gaia.l.a.r\",[]]", "periodic_report_time": **********.655546}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "c12b57d9-4d7f-477d-9940-648823f89482"}}, "invalidation": {"per_sender_topics_to_handler": {"*************": {}, "**********": {}}}, "media": {"device_id_salt": "DE1DF48F8AAA0A1CD9061992C57C9173"}, "media_router": {"receiver_id_hash_token": "IRhXGRMWLTUwPXiaSuZ6hNzVGzGBUZI4FUq6M1/A6JcHRyj8CW8+Cg26TR651iCDMTVX9WCjpKkc5TTKEsgvtQ=="}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}, "last_fetch_attempt": "*****************"}, "predictionmodelfetcher": {"last_fetch_attempt": "*****************", "last_fetch_success": "*****************"}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true}, "store_file_paths_to_delete": {"C:\\Users\\<USER>\\Desktop\\Insta_Sniper_Free_Version\\core_1\\Data\\profile\\System Profile\\optimization_guide_prediction_model_downloads\\86bacb45-ff3d-4a15-8cdf-5c9fc2cdcf6e": true}}, "plugins": {"plugins_list": []}, "profile": {"avatar_index": 26, "content_settings": {"enable_quiet_permission_ui_enabling_method": {"notifications": 1}, "exceptions": {"accessibility_events": {}, "app_banner": {}, "ar": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "client_hints": {}, "clipboard": {}, "cookies": {}, "durable_storage": {}, "fedcm_active_session": {}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "get_display_media_set_select_all_screens": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "legacy_cookie_access": {}, "local_fonts": {}, "media_engagement": {}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "popups": {}, "ppapi_broker": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {}, "sound": {}, "ssl_cert_decisions": {}, "storage_access": {}, "subresource_filter": {}, "subresource_filter_data": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "webid_api": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "107.0.5304.88", "creation_time": "*****************", "last_time_obsolete_http_credentials_removed": **********.256148, "managed_user_id": "", "name": "Person 1", "password_account_storage_settings": {}, "were_old_google_logins_removed": true}, "safebrowsing": {"event_timestamps": {}, "metrics_last_log_time": "***********"}, "sessions": {"session_data_status": 1}, "signin": {"allowed": false}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"requested": false}, "unified_consent": {"migration_state": 10}}