  



#*==========================*#
#* Import/Load Data Section *#
#*==========================*#
#region
import streamlit as st
import streamlit.components.v1 as components
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.webdriver import WebDriver
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from selenium.webdriver.common.action_chains import ActionChains
from webdriver_manager.chrome import ChromeDriverManager
from configparser import ConfigParser
import time
import itertools
import os
import re
import pymongo
from pymongo import MongoClient
import sys
cwd = os.getcwd()
import webbrowser

# prog import
import prog

#endregion



#*====================*#
#* Config.ini Section *#
#*====================*#
#region
### !Loading config.ini Data/Variables ###
config_file = "config.ini"
parser = ConfigParser()
parser.read(config_file, encoding='utf-8')

# Get Data from [targeted page data] Section
user_id = parser["User_Data"]["user_id"]
core = parser["Program_Data"]["core"]
profile = parser["Program_Data"]["profile"]
competitor_link = parser["Program_Data"]["competitor_link"]


#! Config Dictionary
parser = ConfigParser()
parser.read("config.ini", encoding='utf-8')
config_dictionary = {}
for section in parser.sections():
    config_dictionary[section] = {}
    for option in parser.options(section):
        config_dictionary[section][option] = parser.get(section, option)

#endregion





#*===================*#
#* State_101 Section *#
#*===================*#
#region
# Execute User_ID Check Function
prog.state_101() 

#endregion
     



#*=======================*#
#* Page Config Functions *#
#*=======================*#
#region

### !📦Function: config_dictionary_generator ###
#!▶️▶️▶️
def config_dictionary_generator():
    parser = ConfigParser()
    parser.read("config.ini", encoding='utf-8')
    config_dictionary = {}
    for section in parser.sections():
        config_dictionary[section] = {}
        for option in parser.options(section):
            config_dictionary[section][option] = parser.get(section, option)
    return config_dictionary


### !📦Function: generate_config_sections_tuple ###
#!▶️▶️▶️
def generate_config_sections_tuple():
    parser = ConfigParser()
    parser.read("config.ini", encoding='utf-8')
    config_sections_list = parser.sections()
    config_sections_tuple = tuple(config_sections_list)
    return config_sections_tuple


### !📦Function: generate_config_section_options_tuple ###
#!▶️▶️▶️
def generate_config_section_options_tuple(section):
    parser = ConfigParser()
    parser.read("config.ini", encoding='utf-8')
    config_section_options_list = parser.options(section)
    config_section_options_tuple = tuple(config_section_options_list)
    return config_section_options_tuple










#*=======================*#
#*=======================*#
#* Streamlit GUI Section *#
#*=======================*#
#*=======================*#
#region

### !START: Primary GUI Config #######
#region
# Set Page Config: "Phantom - Anti-Detect"
st.set_page_config(
    page_title = "Insta-Sniper",
    page_icon  = '🌀'
)
#endregion

#! Streamlit Remove App footer
#region 
hide_st_style = """
            <style>
            footer {visibility: hidden;}
            </style>
            """
st.markdown(hide_st_style, unsafe_allow_html=True)
#endregion


#! Streamlit sidebar #####
#region
main_menu_options = st.sidebar.selectbox(
    "Insta Sniper Project",
    ("🧾 About","⚙️ Insta-Sniper FREE Version", "⭐ Insta-Sniper Pro [Unlimited Version]")
)
#endregion




#!===================!#
#! If 'About' Option !#
#!===================!#

#region
if main_menu_options == "🧾 About":
    with st.container():
        st.subheader("🧾 About Insta-Sniper IM-BOT | Get your commpetitors followers DATA NOW !!")

        st.image("./img/about_footer.png")

        st.write("Insta-Sniper is an Instagram marketing software/BOT that can scrape your competitors targeted followers data to build your highly targeted Instagram audience for your online business.")
        st.write("By Cyborg TEAM :\n * Abdelkarim Ben Mohammadi\n* Nassima Haddadi\n * Cyborg ULTRA AI")


#endregion




#!=======================================!#
#! If 'Insta-Sniper FREE Version' Option !#
#!=======================================!#
#region
if main_menu_options == "⚙️ Insta-Sniper FREE Version":

    st.subheader("🌀 Launch  your Insta-Sniper IM-BOT Campaign | Get your commpetitors followers DATA NOW !! ")
    col1, col2 = st.columns([1, 3])
    with col1:
        st.image("./img/insta_logo.png", width=150)
    with col2:
        st.write(" ")
        st.write(" ")
        st.write("Insta-Sniper is an Instagram marketing software/BOT that can scrape your competitors targeted followers data to build your highly targeted Instagram audience for your online business.")

    st.text("⚙️ Run Instasniper : ")
    run_instasniper_button = st.button("Run Instasniper BOT")
    if run_instasniper_button:
        prog.program()

 #endregion


#!=======================================!#
#! If 'Insta-Sniper Pro [Unlimited Version]' Option !#
#!=======================================!#
#region
if main_menu_options == "⭐ Insta-Sniper Pro [Unlimited Version]":

    st.title("⭐ Insta-Sniper Pro Unlimited Version | Get as many competitors followers DATA AS YOU WANT | GO FASTER & Be UNLIMITED !! ")
    col3, col4, col5 = st.columns(3)
    with col4:
        st.image("./img/golden_instalogo.png", width=200)

    st.subheader("✔️ Grow your online business with HIGH-QUALITY Instagram Followers Automatically")
    st.subheader("✔️ BOOST YOUR TARGETED ORGANIC TRAFFIC FOR FREE EVERYDAY")
    st.subheader("✔️ Automate your targeted traffic generation WITHOUT ADS or PAID Camapign")
    st.subheader("✔️ Build your targetd audience on AUTOPILOTE")
    st.subheader("✔️ Get your Competitors TARGETED FOLLOWERS DATA on AUTOPILOTE and benefit from their efforts EASILY ..")
    st.subheader("✔️ BOOST your Social Media Marketing Campaigns with the Targeted DATA of your Competitors with EASE !!")
    st.subheader("✔️ Get Big Results on your Online Business With the Super-Power and the High-Speed of the Trending Ai and Data-science BOTs Technology !!")
    st.subheader("And you can get More And More BENIFITS from this BOTs technolgy ...")
    st.subheader(" ")

    col6, col7, col8 = st.columns([1, 3, 1])
    with col6:
        st.image("./img/arrow_red_right.png")
    with col7:
        get_info_button = st.button("""Contact Us NOW to Get More Info about 'Insta-Sniper Pro Unlimited Version' and all our others Instagram Traffic & Autopilote Marketing BOTs Projects !!""")
        if get_info_button :
            get_more_info_form_url = "https://forms.gle/DxHC77cq7rcPQ5qb6"
            webbrowser.open_new_tab(get_more_info_form_url)
    with col8:
        st.image("./img/arrow_red_left.png")



















