{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13394652873667789", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "page_ordinal": "n", "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Insta_Snipe\\core_1\\App\\Chrome-bin\\107.0.5304.88\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": [], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13394652873668702", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Insta_Snipe\\core_1\\App\\Chrome-bin\\107.0.5304.88\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "neajdppkdcdipfabeoofebfddakdcjhd": {"active_permissions": {"api": ["systemPrivate", "ttsEngine"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13394652873670730", "location": 5, "manifest": {"background": {"persistent": false, "scripts": ["tts_extension.js"]}, "description": "Component extension providing speech via the Google network text-to-speech service.", "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA8GSbNUMGygqQTNDMFGIjZNcwXsHLzkNkHjWbuY37PbNdSDZ4VqlVjzbWqODSe+MjELdv5Keb51IdytnoGYXBMyqKmWpUrg+RnKvQ5ibWr4MW9pyIceOIdp9GrzC1WZGgTmZismYR3AjaIpufZ7xDdQQv+XrghPWCkdVqLN+qZDA1HU+DURznkMICiDDSH2sU0egm9UbWfS218bZqzKeQDiC3OnTPlaxcbJtKUuupIm5knjze3Wo9Ae9poTDMzKgchg0VlFCv3uqox+wlD8sjXBoyBCCK9HpImdVAF1a7jpdgiUHpPeV/26oYzM9/grltwNR3bzECQgSpyXp0eyoegwIDAQAB", "manifest_version": 2, "name": "Google Network Speech", "permissions": ["systemPrivate", "ttsEngine", "https://www.google.com/"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Insta_Snipe\\core_1\\App\\Chrome-bin\\107.0.5304.88\\resources\\network_speech_synthesis", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"active_permissions": {"api": ["desktopCapture", "processes", "webrtcAudioPrivate", "webrtcDesktopCapturePrivate", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "events": ["runtime.onConnectExternal"], "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13394652873669983", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"matches": ["https://*.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["desktopCapture", "enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcAudioPrivate", "webrtcDesktopCapturePrivate", "webrtcLoggingPrivate"], "version": "1.3.21"}, "path": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Insta_Snipe\\core_1\\App\\Chrome-bin\\107.0.5304.88\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "state": 1, "was_installed_by_default": false, "was_installed_by_oem": false}, "nmmhkkegccagdldgiimedpiccmgmieda": {"ack_external": true, "active_bit": false, "active_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 137, "events": ["app.runtime.onLaunched", "runtime.onConnectExternal"], "from_webstore": true, "granted_permissions": {"api": ["identity", "webview"], "explicit_host": ["https://payments.google.com/*", "https://sandbox.google.com/*", "https://www.google.com/*", "https://www.googleapis.com/*"], "manifest_permissions": [], "scriptable_host": []}, "incognito_content_settings": [], "incognito_preferences": {}, "install_time": "13394652878944045", "lastpingday": "13394617200814667", "location": 10, "manifest": {"app": {"background": {"scripts": ["craw_background.js"]}}, "current_locale": "en_US", "default_locale": "en", "description": "Chrome Web Store Payments", "display_in_launcher": false, "display_in_new_tab_page": false, "icons": {"128": "images/icon_128.png", "16": "images/icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCrKfMnLqViEyokd1wk57FxJtW2XXpGXzIHBzv9vQI/01UsuP0IV5/lj0wx7zJ/xcibUgDeIxobvv9XD+zO1MdjMWuqJFcKuSS4Suqkje6u+pMrTSGOSHq1bmBVh0kpToN8YoJs/P/yrRd7FEtAXTaFTGxQL4C385MeXSjaQfiRiQIDAQAB", "manifest_version": 2, "minimum_chrome_version": "29", "name": "Chrome Web Store Payments", "oauth2": {"auto_approve": true, "client_id": "203784468217.apps.googleusercontent.com", "scopes": ["https://www.googleapis.com/auth/sierra", "https://www.googleapis.com/auth/sierrasandbox", "https://www.googleapis.com/auth/chromewebstore", "https://www.googleapis.com/auth/chromewebstore.readonly"]}, "permissions": ["identity", "webview", "https://www.google.com/", "https://www.googleapis.com/*", "https://payments.google.com/payments/v4/js/integrator.js", "https://sandbox.google.com/payments/v4/js/integrator.js"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.0.0.6"}, "path": "nmmhkkegccagdldgiimedpiccmgmieda\\1.0.0.6_1", "preferences": {}, "regular_only_preferences": {}, "running": false, "state": 1, "was_installed_by_default": true, "was_installed_by_oem": false}}}, "pinned_tabs": [], "prefs": {"preference_reset_time": "13394652873655352"}, "protection": {"macs": {"browser": {"show_home_button": "5176B83D12E4270AD500ED514A7A294C78BE5107C317AAD3E7B69EE0C19C54E3"}, "default_search_provider_data": {"template_url_data": "F3DE33A724E3978E3F4A612A1097C7590E0D1C2C2EBB5618DFACA7CA798987AD"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "8CF494B9EA757B4759B1B989404F98EDA51F5BEF1EA6F9E0AEF0C1D63383DE05", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "9E1C98E6761E8956DE213F3071AD51C8123DA5483FDA848FFFE65D8E20200D57", "neajdppkdcdipfabeoofebfddakdcjhd": "0F5D08F47715A970B73947D6A66548A7825C2D6DD16E32C5A0C19C7CCD490449", "nkeimhogjdpnpccoofpliimaahmaaome": "6E332228DA7D82571F6383FB1A1A55B36593DFD30C40847D227C9841616CE743", "nmmhkkegccagdldgiimedpiccmgmieda": "F35A0FBB58B16CD811D92EF4BC051D9CC06E6EDFA9682CA7316056987662EAAC"}}, "google": {"services": {"account_id": "67D11F8DCF3D6AED5B0177B17FEFF4358D63D38824EB46E223716F27CF03BCAD", "last_account_id": "667754CBDAABEC4F388D1B8F57DE49744A2F93C70F36D565257E70107F94C057", "last_username": "F8FDFBE7C773F1778C603D99E579EB593F1797B69DB8C8A80762D33FF37FB49C"}}, "homepage": "BFDA714A5A4E52897B16DD91066E45B38BEF20C1B7040C8E58BCBA0903C7B0A2", "homepage_is_newtabpage": "5C4C38A481EF24A732D5597ADEB71F48E8B7768DF60760D21966514799916F20", "media": {"cdm": {"origin_data": "42475CBC846C56B58E6787E9E2489E4AB5C3F29DD81DD0E562BA9ADBE726F2E2"}, "storage_id_salt": "E890B66288588FBD36924DFA6FEBE5443D6E51B02B0885FC4E28FBECB4A16B21"}, "module_blocklist_cache_md5_digest": "65C51A61EB3828223DCF904B429CDA74CABC51EBBBA282E222AC412420138403", "pinned_tabs": "90333D5E3C7BAD9AAC667308CB1388C376EDA620B9CD76AB68BD83B87D905140", "prefs": {"preference_reset_time": "67A0F417EE200117160C0C3C26564821BD3146BDE973136E519E9E943FCE16D3"}, "safebrowsing": {"incidents_sent": "E83F7382FEA9DE302768F1C0B0923C0FF5B5FEAE4D4C152CFD472D7FD1F7984B"}, "search_provider_overrides": "CD15022020A7F47DE715DC364BABB2326FC37968E0E65F049B4EC2D8EFE248FD", "session": {"restore_on_startup": "FEB9AFDA86D9845F6C2609B487DBE60F25890B68E0345C0A628C6CC292025AAB", "startup_urls": "8598FAD975FA7FBF9D71F27C0F034D1EC00BA9FDD0E8B20C97A2D0D23450E8B4"}, "settings_reset_prompt": {"last_triggered_for_default_search": "938CB8AD9989752D19A9728B524E72EEF22C5F6AD8A92DA0688642AC74D32D9C", "last_triggered_for_homepage": "CE7AB1CD538185734BE8976C70D191DC853187E6A3DC7BC670B5FFD0F6EE6E4F", "last_triggered_for_startup_urls": "245E5F5BC5E59C71FC281A840CAA0960FEEE2A38BF5B89B8392956422E29333D", "prompt_wave": "B2308B4EEB72BCCAFF0F20E8E4F4B5268BD4BA66706724A0D9646F069F55FA4C"}, "software_reporter": {"prompt_seed": "7FACD11EADECC10560B3F03C915338549427C5E3FDCD499250F0A2101549069C", "prompt_version": "1C52C50992556F46D9957698C47CB153F7452BCF7CC25568CB17969EBAC9E98F", "reporting": "0A32BC01C2B6C741D787E28E018BAE26CC85F66B7E0C010717495422447A8714"}}, "super_mac": "F9C27E5A7F1A4431E6829D1DA886120541768F3FB2D862E3DC13E76CA9ED09B0"}}